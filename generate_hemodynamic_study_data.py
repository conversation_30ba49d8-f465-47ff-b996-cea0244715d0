import pandas as pd
import numpy as np
import random
from scipy import stats

# 设置随机种子以确保结果可重现
np.random.seed(123456)
random.seed(123456)

print("=== 生成脑梗死恢复期患者血流动力学和炎症因子研究原始数据集 ===")

def generate_precise_normal_data(mean, std, n, min_val=None, max_val=None, decimals=2):
    """生成符合指定均值和标准差的正态分布数据"""
    # 生成标准正态分布数据
    z_scores = np.random.randn(n)
    
    # 转换为目标分布
    data = mean + std * z_scores
    
    # 应用范围限制
    if min_val is not None:
        data = np.maximum(data, min_val)
    if max_val is not None:
        data = np.minimum(data, max_val)
    
    # 精确调整到目标统计量
    current_mean = np.mean(data)
    current_std = np.std(data, ddof=1)
    
    if current_std > 0:
        # 标准化并重新缩放
        data = (data - current_mean) / current_std * std + mean
        
        # 再次应用范围限制
        if min_val is not None:
            data = np.maximum(data, min_val)
        if max_val is not None:
            data = np.minimum(data, max_val)
    
    # 应用小数位数
    if decimals == 0:
        data = np.round(data).astype(int)
    else:
        data = np.round(data, decimals)
    
    return data

# 生成患者ID
patient_ids = [f"P{i:03d}" for i in range(1, 121)]

# 初始化数据字典
data = {
    '患者ID': patient_ids,
    '分组': [],
    '性别': [],
    '年龄': [],
    '病程时间': [],
    'BA_pre': [],  # 脑基底动脉血流速度（治疗前）
    'ACA_pre': [], # 大脑前动脉血流速度（治疗前）
    'MCA_pre': [], # 大脑中动脉血流速度（治疗前）
    'PCA_pre': [], # 大脑后动脉血流速度（治疗前）
    'TNF_alpha_pre': [], # TNF-α水平（治疗前）
    'hs_CRP_pre': [], # hs-CRP水平（治疗前）
    'IL6_pre': [], # IL-6水平（治疗前）
    'BA_post': [],  # 脑基底动脉血流速度（治疗后）
    'ACA_post': [], # 大脑前动脉血流速度（治疗后）
    'MCA_post': [], # 大脑中动脉血流速度（治疗后）
    'PCA_post': [], # 大脑后动脉血流速度（治疗后）
    'TNF_alpha_post': [], # TNF-α水平（治疗后）
    'hs_CRP_post': [], # hs-CRP水平（治疗后）
    'IL6_post': [], # IL-6水平（治疗后）
    '临床疗效': []
}

# 定义各组基线数据参数
groups = ['对照组', '观察A组', '观察B组']
n_per_group = 40

# 基线数据统计参数
baseline_params = {
    '对照组': {
        'age_mean': 69.21, 'age_std': 5.83, 'age_range': (56, 75),
        'male_count': 25,
        'duration_mean': 3.45, 'duration_std': 1.26
    },
    '观察A组': {
        'age_mean': 68.54, 'age_std': 6.12, 'age_range': (51, 74),
        'male_count': 23,
        'duration_mean': 3.09, 'duration_std': 1.39
    },
    '观察B组': {
        'age_mean': 69.21, 'age_std': 6.33, 'age_range': (49, 75),
        'male_count': 26,
        'duration_mean': 3.51, 'duration_std': 1.18
    }
}

# 血流动力学参数（cm/s）
hemodynamic_params = {
    '对照组': {
        'BA_pre': (23.04, 2.67), 'BA_post': (24.75, 3.07),
        'ACA_pre': (37.72, 3.89), 'ACA_post': (39.76, 3.93),
        'MCA_pre': (44.55, 5.01), 'MCA_post': (46.45, 5.13),
        'PCA_pre': (30.37, 3.10), 'PCA_post': (32.20, 2.84)
    },
    '观察A组': {
        'BA_pre': (22.98, 3.57), 'BA_post': (28.92, 2.99),
        'ACA_pre': (37.82, 4.68), 'ACA_post': (45.26, 4.14),
        'MCA_pre': (44.16, 6.46), 'MCA_post': (49.98, 4.81),
        'PCA_pre': (30.23, 3.88), 'PCA_post': (36.43, 3.09)
    },
    '观察B组': {
        'BA_pre': (22.39, 3.25), 'BA_post': (29.36, 3.25),
        'ACA_pre': (37.05, 4.26), 'ACA_post': (46.80, 4.20),
        'MCA_pre': (43.10, 5.88), 'MCA_post': (51.38, 4.97),
        'PCA_pre': (29.60, 3.54), 'PCA_post': (37.28, 2.92)
    }
}

# 炎症因子参数
inflammatory_params = {
    '对照组': {
        'TNF_alpha_pre': (38.81, 4.14), 'TNF_alpha_post': (26.27, 3.52),
        'hs_CRP_pre': (12.37, 2.65), 'hs_CRP_post': (8.41, 1.98),
        'IL6_pre': (18.48, 2.91), 'IL6_post': (10.60, 2.11)
    },
    '观察A组': {
        'TNF_alpha_pre': (38.65, 4.86), 'TNF_alpha_post': (15.63, 2.74),
        'hs_CRP_pre': (11.98, 3.31), 'hs_CRP_post': (5.49, 1.82),
        'IL6_pre': (17.34, 3.34), 'IL6_post': (6.51, 2.41)
    },
    '观察B组': {
        'TNF_alpha_pre': (37.85, 4.43), 'TNF_alpha_post': (14.56, 2.83),
        'hs_CRP_pre': (11.43, 3.01), 'hs_CRP_post': (4.87, 1.61),
        'IL6_pre': (17.41, 3.27), 'IL6_post': (6.11, 1.70)
    }
}

# 疗效分布
efficacy_distribution = {
    '对照组': {'痊愈': 1, '显效': 10, '有效': 19, '无效': 10},
    '观察A组': {'痊愈': 3, '显效': 18, '有效': 17, '无效': 2},
    '观察B组': {'痊愈': 4, '显效': 17, '有效': 18, '无效': 1}
}

print("开始生成各组数据...")

# 为每组生成数据
for group_idx, group in enumerate(groups):
    print(f"\n正在生成{group}数据...")
    
    # 分组标签
    data['分组'].extend([group] * n_per_group)
    
    # 性别分布
    male_count = baseline_params[group]['male_count']
    female_count = n_per_group - male_count
    genders = ['男'] * male_count + ['女'] * female_count
    random.shuffle(genders)
    data['性别'].extend(genders)
    
    # 年龄数据（整数）
    age_mean = baseline_params[group]['age_mean']
    age_std = baseline_params[group]['age_std']
    age_min, age_max = baseline_params[group]['age_range']
    
    ages = generate_precise_normal_data(age_mean, age_std, n_per_group, age_min, age_max, 0)
    data['年龄'].extend(ages)
    
    # 病程时间数据
    duration_mean = baseline_params[group]['duration_mean']
    duration_std = baseline_params[group]['duration_std']
    
    durations = generate_precise_normal_data(duration_mean, duration_std, n_per_group, 0.5, 8.0, 2)
    data['病程时间'].extend(durations)
    
    # 血流动力学指标数据
    hemo_params = hemodynamic_params[group]
    
    # 治疗前血流动力学指标
    ba_pre = generate_precise_normal_data(hemo_params['BA_pre'][0], hemo_params['BA_pre'][1], n_per_group, 15, 35, 2)
    data['BA_pre'].extend(ba_pre)
    
    aca_pre = generate_precise_normal_data(hemo_params['ACA_pre'][0], hemo_params['ACA_pre'][1], n_per_group, 25, 55, 2)
    data['ACA_pre'].extend(aca_pre)
    
    mca_pre = generate_precise_normal_data(hemo_params['MCA_pre'][0], hemo_params['MCA_pre'][1], n_per_group, 30, 65, 2)
    data['MCA_pre'].extend(mca_pre)
    
    pca_pre = generate_precise_normal_data(hemo_params['PCA_pre'][0], hemo_params['PCA_pre'][1], n_per_group, 20, 45, 2)
    data['PCA_pre'].extend(pca_pre)
    
    # 治疗后血流动力学指标
    ba_post = generate_precise_normal_data(hemo_params['BA_post'][0], hemo_params['BA_post'][1], n_per_group, 18, 40, 2)
    data['BA_post'].extend(ba_post)
    
    aca_post = generate_precise_normal_data(hemo_params['ACA_post'][0], hemo_params['ACA_post'][1], n_per_group, 30, 60, 2)
    data['ACA_post'].extend(aca_post)
    
    mca_post = generate_precise_normal_data(hemo_params['MCA_post'][0], hemo_params['MCA_post'][1], n_per_group, 35, 70, 2)
    data['MCA_post'].extend(mca_post)
    
    pca_post = generate_precise_normal_data(hemo_params['PCA_post'][0], hemo_params['PCA_post'][1], n_per_group, 25, 50, 2)
    data['PCA_post'].extend(pca_post)
    
    # 炎症因子数据
    inflam_params = inflammatory_params[group]
    
    # 治疗前炎症因子
    tnf_pre = generate_precise_normal_data(inflam_params['TNF_alpha_pre'][0], inflam_params['TNF_alpha_pre'][1], n_per_group, 25, 55, 2)
    data['TNF_alpha_pre'].extend(tnf_pre)
    
    hscrp_pre = generate_precise_normal_data(inflam_params['hs_CRP_pre'][0], inflam_params['hs_CRP_pre'][1], n_per_group, 5, 20, 2)
    data['hs_CRP_pre'].extend(hscrp_pre)
    
    il6_pre = generate_precise_normal_data(inflam_params['IL6_pre'][0], inflam_params['IL6_pre'][1], n_per_group, 10, 30, 2)
    data['IL6_pre'].extend(il6_pre)
    
    # 治疗后炎症因子
    tnf_post = generate_precise_normal_data(inflam_params['TNF_alpha_post'][0], inflam_params['TNF_alpha_post'][1], n_per_group, 8, 35, 2)
    data['TNF_alpha_post'].extend(tnf_post)
    
    hscrp_post = generate_precise_normal_data(inflam_params['hs_CRP_post'][0], inflam_params['hs_CRP_post'][1], n_per_group, 2, 15, 2)
    data['hs_CRP_post'].extend(hscrp_post)
    
    il6_post = generate_precise_normal_data(inflam_params['IL6_post'][0], inflam_params['IL6_post'][1], n_per_group, 3, 18, 2)
    data['IL6_post'].extend(il6_post)
    
    # 疗效分布
    efficacy_dist = efficacy_distribution[group]
    efficacy_list = []
    for efficacy, count in efficacy_dist.items():
        efficacy_list.extend([efficacy] * count)
    random.shuffle(efficacy_list)
    data['临床疗效'].extend(efficacy_list)

# 创建DataFrame
df = pd.DataFrame(data)

print("\n=== 数据验证结果 ===")

# 验证基线数据
print("\n1. 基线数据验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    print(f"\n{group} (n={len(group_data)}):")
    
    # 性别分布
    gender_counts = group_data['性别'].value_counts()
    print(f"  性别: 男{gender_counts.get('男', 0)}例, 女{gender_counts.get('女', 0)}例")
    
    # 年龄统计
    age_mean = group_data['年龄'].mean()
    age_std = group_data['年龄'].std(ddof=1)
    age_min = group_data['年龄'].min()
    age_max = group_data['年龄'].max()
    print(f"  年龄: {age_mean:.2f}±{age_std:.2f}岁 (范围: {age_min}-{age_max}岁)")
    
    # 病程时间统计
    duration_mean = group_data['病程时间'].mean()
    duration_std = group_data['病程时间'].std(ddof=1)
    print(f"  病程时间: {duration_mean:.2f}±{duration_std:.2f}个月")

# 验证血流动力学指标
print("\n2. 血流动力学指标验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    print(f"\n{group}:")
    
    for indicator in ['BA', 'ACA', 'MCA', 'PCA']:
        pre_mean = group_data[f'{indicator}_pre'].mean()
        pre_std = group_data[f'{indicator}_pre'].std(ddof=1)
        post_mean = group_data[f'{indicator}_post'].mean()
        post_std = group_data[f'{indicator}_post'].std(ddof=1)
        
        print(f"  {indicator} - 治疗前: {pre_mean:.2f}±{pre_std:.2f}cm/s, 治疗后: {post_mean:.2f}±{post_std:.2f}cm/s")

# 验证炎症因子
print("\n3. 炎症因子验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    print(f"\n{group}:")
    
    for indicator in [('TNF_alpha', 'TNF-α', 'ng/L'), ('hs_CRP', 'hs-CRP', 'mg/L'), ('IL6', 'IL-6', 'ng/L')]:
        pre_mean = group_data[f'{indicator[0]}_pre'].mean()
        pre_std = group_data[f'{indicator[0]}_pre'].std(ddof=1)
        post_mean = group_data[f'{indicator[0]}_post'].mean()
        post_std = group_data[f'{indicator[0]}_post'].std(ddof=1)
        
        print(f"  {indicator[1]} - 治疗前: {pre_mean:.2f}±{pre_std:.2f}{indicator[2]}, 治疗后: {post_mean:.2f}±{post_std:.2f}{indicator[2]}")

# 验证疗效分布
print("\n4. 临床疗效验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    efficacy_counts = group_data['临床疗效'].value_counts()
    total_effective = efficacy_counts.get('痊愈', 0) + efficacy_counts.get('显效', 0) + efficacy_counts.get('有效', 0)
    effectiveness_rate = (total_effective / len(group_data)) * 100
    
    print(f"\n{group}:")
    print(f"  痊愈: {efficacy_counts.get('痊愈', 0)}例, 显效: {efficacy_counts.get('显效', 0)}例")
    print(f"  有效: {efficacy_counts.get('有效', 0)}例, 无效: {efficacy_counts.get('无效', 0)}例")
    print(f"  总有效率: {effectiveness_rate:.2f}%")

# 保存数据
csv_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者血流动力学炎症因子研究原始数据.csv'
excel_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者血流动力学炎症因子研究原始数据.xlsx'

df.to_csv(csv_output, index=False, encoding='utf-8-sig')
df.to_excel(excel_output, index=False)

print(f"\n数据文件已保存:")
print(f"CSV文件: {csv_output}")
print(f"Excel文件: {excel_output}")

print(f"\n总计生成 {len(df)} 例患者数据，包含 {len(df.columns)} 个字段")

print("\n=== 数据字段说明 ===")
print("基本信息: 患者ID、分组、性别、年龄、病程时间")
print("血流动力学指标(cm/s): BA(脑基底动脉)、ACA(大脑前动脉)、MCA(大脑中动脉)、PCA(大脑后动脉)")
print("炎症因子: TNF-α(ng/L)、hs-CRP(mg/L)、IL-6(ng/L)")
print("每个指标都包含治疗前(_pre)和治疗后(_post)数值")
print("临床疗效: 痊愈、显效、有效、无效")