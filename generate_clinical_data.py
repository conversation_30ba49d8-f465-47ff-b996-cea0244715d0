import pandas as pd
import numpy as np
import random
from scipy import stats

# 设置随机种子以确保结果可重现
np.random.seed(42)
random.seed(42)

def generate_normal_data(mean, std, n, min_val=None, max_val=None, decimals=None):
    """生成符合正态分布的数据"""
    data = np.random.normal(mean, std, n)
    
    # 应用范围限制
    if min_val is not None:
        data = np.maximum(data, min_val)
    if max_val is not None:
        data = np.minimum(data, max_val)
    
    # 应用小数位数限制
    if decimals is not None:
        if decimals == 0:
            data = np.round(data).astype(int)
        else:
            data = np.round(data, decimals)
    
    return data

def adjust_to_target_stats(data, target_mean, target_std):
    """调整数据以匹配目标均值和标准差"""
    current_mean = np.mean(data)
    current_std = np.std(data, ddof=1)
    
    # 标准化然后重新缩放
    normalized = (data - current_mean) / current_std
    adjusted = normalized * target_std + target_mean
    
    return adjusted

# 生成患者ID
patient_ids = [f"P{i:03d}" for i in range(1, 136)]

# 初始化数据字典
data = {
    '患者ID': patient_ids,
    '分组': [],
    '性别': [],
    '年龄': [],
    '病程': [],
    '治疗前HAMA评分': [],
    '治疗后HAMA评分': [],
    '治疗前HAMD评分': [],
    '治疗后HAMD评分': [],
    '治疗前SS-QOL评分': [],
    '治疗后SS-QOL评分': [],
    '中医证候疗效': []
}

# 定义每组数据
groups = ['对照组', '观察A组', '观察B组']
n_per_group = 45

# 基础数据统计参数
baseline_stats = {
    '对照组': {
        'male_count': 28,
        'age_mean': 66.38, 'age_std': 6.34, 'age_range': (50, 75),
        'duration_mean': 4.06, 'duration_std': 1.43
    },
    '观察A组': {
        'male_count': 25,
        'age_mean': 67.06, 'age_std': 6.89, 'age_range': (49, 75),
        'duration_mean': 4.11, 'duration_std': 1.66
    },
    '观察B组': {
        'male_count': 26,
        'age_mean': 65.53, 'age_std': 6.54, 'age_range': (49, 75),
        'duration_mean': 3.98, 'duration_std': 1.85
    }
}

# HAMA评分统计参数
hama_stats = {
    '对照组': {'pre': (24.58, 4.80), 'post': (20.88, 3.40)},
    '观察A组': {'pre': (24.41, 6.16), 'post': (18.43, 3.90)},
    '观察B组': {'pre': (22.52, 5.77), 'post': (14.05, 2.57)}
}

# HAMD评分统计参数
hamd_stats = {
    '对照组': {'pre': (18.22, 2.96), 'post': (15.72, 3.23)},
    '观察A组': {'pre': (17.79, 3.62), 'post': (13.12, 3.05)},
    '观察B组': {'pre': (17.24, 3.23), 'post': (9.33, 2.87)}
}

# SS-QOL评分统计参数
ssqol_stats = {
    '对照组': {'pre': (162.58, 10.06), 'post': (169.87, 10.65)},
    '观察A组': {'pre': (161.12, 12.30), 'post': (180.18, 10.75)},
    '观察B组': {'pre': (159.39, 11.37), 'post': (198.98, 11.20)}
}

# 疗效分布
efficacy_distribution = {
    '对照组': {'痊愈': 1, '显效': 9, '有效': 21, '无效': 14},
    '观察A组': {'痊愈': 2, '显效': 18, '有效': 19, '无效': 6},
    '观察B组': {'痊愈': 5, '显效': 16, '有效': 21, '无效': 3}
}

# 为每组生成数据
for group_idx, group in enumerate(groups):
    start_idx = group_idx * n_per_group
    end_idx = (group_idx + 1) * n_per_group
    
    # 分组标签
    data['分组'].extend([group] * n_per_group)
    
    # 性别分布
    male_count = baseline_stats[group]['male_count']
    female_count = n_per_group - male_count
    genders = ['男'] * male_count + ['女'] * female_count
    random.shuffle(genders)
    data['性别'].extend(genders)
    
    # 年龄数据（整数）
    age_mean = baseline_stats[group]['age_mean']
    age_std = baseline_stats[group]['age_std']
    age_min, age_max = baseline_stats[group]['age_range']
    
    ages = generate_normal_data(age_mean, age_std, n_per_group, age_min, age_max, 0)
    ages = adjust_to_target_stats(ages, age_mean, age_std)
    ages = np.clip(np.round(ages), age_min, age_max).astype(int)
    data['年龄'].extend(ages)
    
    # 病程数据（1-2位小数）
    duration_mean = baseline_stats[group]['duration_mean']
    duration_std = baseline_stats[group]['duration_std']
    
    durations = generate_normal_data(duration_mean, duration_std, n_per_group, 0.5, 8.0, 2)
    durations = adjust_to_target_stats(durations, duration_mean, duration_std)
    durations = np.clip(durations, 0.5, 8.0)
    data['病程'].extend(durations)
    
    # HAMA评分数据
    hama_pre_mean, hama_pre_std = hama_stats[group]['pre']
    hama_post_mean, hama_post_std = hama_stats[group]['post']
    
    hama_pre = generate_normal_data(hama_pre_mean, hama_pre_std, n_per_group, 0, 56, 1)
    hama_pre = adjust_to_target_stats(hama_pre, hama_pre_mean, hama_pre_std)
    hama_pre = np.clip(hama_pre, 0, 56)
    data['治疗前HAMA评分'].extend(hama_pre)
    
    hama_post = generate_normal_data(hama_post_mean, hama_post_std, n_per_group, 0, 56, 1)
    hama_post = adjust_to_target_stats(hama_post, hama_post_mean, hama_post_std)
    hama_post = np.clip(hama_post, 0, 56)
    data['治疗后HAMA评分'].extend(hama_post)
    
    # HAMD评分数据
    hamd_pre_mean, hamd_pre_std = hamd_stats[group]['pre']
    hamd_post_mean, hamd_post_std = hamd_stats[group]['post']
    
    hamd_pre = generate_normal_data(hamd_pre_mean, hamd_pre_std, n_per_group, 0, 54, 1)
    hamd_pre = adjust_to_target_stats(hamd_pre, hamd_pre_mean, hamd_pre_std)
    hamd_pre = np.clip(hamd_pre, 0, 54)
    data['治疗前HAMD评分'].extend(hamd_pre)
    
    hamd_post = generate_normal_data(hamd_post_mean, hamd_post_std, n_per_group, 0, 54, 1)
    hamd_post = adjust_to_target_stats(hamd_post, hamd_post_mean, hamd_post_std)
    hamd_post = np.clip(hamd_post, 0, 54)
    data['治疗后HAMD评分'].extend(hamd_post)
    
    # SS-QOL评分数据
    ssqol_pre_mean, ssqol_pre_std = ssqol_stats[group]['pre']
    ssqol_post_mean, ssqol_post_std = ssqol_stats[group]['post']
    
    ssqol_pre = generate_normal_data(ssqol_pre_mean, ssqol_pre_std, n_per_group, 49, 245, 1)
    ssqol_pre = adjust_to_target_stats(ssqol_pre, ssqol_pre_mean, ssqol_pre_std)
    ssqol_pre = np.clip(ssqol_pre, 49, 245)
    data['治疗前SS-QOL评分'].extend(ssqol_pre)
    
    ssqol_post = generate_normal_data(ssqol_post_mean, ssqol_post_std, n_per_group, 49, 245, 1)
    ssqol_post = adjust_to_target_stats(ssqol_post, ssqol_post_mean, ssqol_post_std)
    ssqol_post = np.clip(ssqol_post, 49, 245)
    data['治疗后SS-QOL评分'].extend(ssqol_post)
    
    # 疗效分布
    efficacy_dist = efficacy_distribution[group]
    efficacy_list = []
    for efficacy, count in efficacy_dist.items():
        efficacy_list.extend([efficacy] * count)
    random.shuffle(efficacy_list)
    data['中医证候疗效'].extend(efficacy_list)

# 创建DataFrame
df = pd.DataFrame(data)

# 验证统计结果
print("=== 数据验证结果 ===")
print("\n1. 基础数据验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    print(f"\n{group} (n={len(group_data)}):")
    
    # 性别分布
    gender_counts = group_data['性别'].value_counts()
    print(f"  性别: 男{gender_counts.get('男', 0)}例, 女{gender_counts.get('女', 0)}例")
    
    # 年龄统计
    age_mean = group_data['年龄'].mean()
    age_std = group_data['年龄'].std(ddof=1)
    age_min = group_data['年龄'].min()
    age_max = group_data['年龄'].max()
    print(f"  年龄: {age_mean:.2f}±{age_std:.2f}岁 (范围: {age_min}-{age_max}岁)")
    
    # 病程统计
    duration_mean = group_data['病程'].mean()
    duration_std = group_data['病程'].std(ddof=1)
    print(f"  病程: {duration_mean:.2f}±{duration_std:.2f}个月")

print("\n2. 量表评分验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    print(f"\n{group}:")
    
    # HAMA评分
    hama_pre_mean = group_data['治疗前HAMA评分'].mean()
    hama_pre_std = group_data['治疗前HAMA评分'].std(ddof=1)
    hama_post_mean = group_data['治疗后HAMA评分'].mean()
    hama_post_std = group_data['治疗后HAMA评分'].std(ddof=1)
    print(f"  HAMA评分 - 治疗前: {hama_pre_mean:.2f}±{hama_pre_std:.2f}, 治疗后: {hama_post_mean:.2f}±{hama_post_std:.2f}")
    
    # HAMD评分
    hamd_pre_mean = group_data['治疗前HAMD评分'].mean()
    hamd_pre_std = group_data['治疗前HAMD评分'].std(ddof=1)
    hamd_post_mean = group_data['治疗后HAMD评分'].mean()
    hamd_post_std = group_data['治疗后HAMD评分'].std(ddof=1)
    print(f"  HAMD评分 - 治疗前: {hamd_pre_mean:.2f}±{hamd_pre_std:.2f}, 治疗后: {hamd_post_mean:.2f}±{hamd_post_std:.2f}")
    
    # SS-QOL评分
    ssqol_pre_mean = group_data['治疗前SS-QOL评分'].mean()
    ssqol_pre_std = group_data['治疗前SS-QOL评分'].std(ddof=1)
    ssqol_post_mean = group_data['治疗后SS-QOL评分'].mean()
    ssqol_post_std = group_data['治疗后SS-QOL评分'].std(ddof=1)
    print(f"  SS-QOL评分 - 治疗前: {ssqol_pre_mean:.2f}±{ssqol_pre_std:.2f}, 治疗后: {ssqol_post_mean:.2f}±{ssqol_post_std:.2f}")

print("\n3. 疗效分布验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    efficacy_counts = group_data['中医证候疗效'].value_counts()
    total_effective = efficacy_counts.get('痊愈', 0) + efficacy_counts.get('显效', 0) + efficacy_counts.get('有效', 0)
    effectiveness_rate = (total_effective / len(group_data)) * 100
    print(f"\n{group}:")
    print(f"  痊愈: {efficacy_counts.get('痊愈', 0)}例, 显效: {efficacy_counts.get('显效', 0)}例")
    print(f"  有效: {efficacy_counts.get('有效', 0)}例, 无效: {efficacy_counts.get('无效', 0)}例")
    print(f"  总有效率: {effectiveness_rate:.2f}%")

# 保存CSV文件
output_file = "/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据.csv"
df.to_csv(output_file, index=False, encoding='utf-8-sig')
print(f"\n数据已保存至: {output_file}")

# 同时保存Excel文件
excel_file = "/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据.xlsx"
df.to_excel(excel_file, index=False)
print(f"数据已保存至: {excel_file}")

print(f"\n总计生成 {len(df)} 例患者数据")