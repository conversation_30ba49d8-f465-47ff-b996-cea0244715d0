import pandas as pd
import numpy as np
import random
from collections import defaultdict

# 设置随机种子
random.seed(42)
np.random.seed(42)

print("=== 开始处理姓名数据整合 ===")

# 1. 读取120例实验记录.xlsx文件，清理数据
print("1. 读取120例实验记录.xlsx...")
names_df = pd.read_excel('/Users/<USER>/Downloads/冯雷/原始数据/120例实验记录.xlsx')

# 找到真正的数据开始行（从序号行开始）
header_row = None
for idx, row in names_df.iterrows():
    if str(row.iloc[0]).strip() == '序号':
        header_row = idx
        break

if header_row is not None:
    # 重新读取，设置正确的header
    names_df = pd.read_excel('/Users/<USER>/Downloads/冯雷/原始数据/120例实验记录.xlsx', 
                            header=header_row)
    # 删除无效行（比如效果标记行）
    names_df = names_df[names_df['序号'].notna()]
    names_df = names_df[names_df['序号'] != '序号']  # 删除重复的header行
    
    # 转换数据类型
    names_df['序号'] = pd.to_numeric(names_df['序号'], errors='coerce')
    names_df['年龄'] = pd.to_numeric(names_df['年龄'], errors='coerce')
    
    # 删除无效行
    names_df = names_df.dropna(subset=['序号', '姓名', '年龄'])
    names_df = names_df[names_df['序号'].notna()]

print(f"清理后的姓名数据形状: {names_df.shape}")
print("姓名数据列:", names_df.columns.tolist())

# 2. 读取脑梗死恢复期患者临床研究原始数据
print("\n2. 读取临床研究数据...")
clinical_df = pd.read_csv('/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_最终版.csv')
print(f"临床数据形状: {clinical_df.shape}")
print("临床数据列:", clinical_df.columns.tolist())

# 3. 按组别和性别分类姓名
print("\n3. 按组别和性别分类姓名...")
name_pools = defaultdict(list)

for _, row in names_df.iterrows():
    name = str(row['姓名']).strip()
    age = int(row['年龄']) if pd.notna(row['年龄']) else 0
    gender = str(row['性别']).strip()
    group = str(row['组别']).strip() if '组别' in names_df.columns else '未知'
    
    if name and age > 0 and gender in ['男', '女']:
        name_pools[f"{group}_{gender}"].append({
            'name': name,
            'age': age,
            'gender': gender,
            'group': group
        })

print("姓名池统计:")
for key, names in name_pools.items():
    print(f"  {key}: {len(names)}个姓名")

# 4. 为临床研究数据匹配姓名
print("\n4. 开始为临床数据匹配姓名...")

# 创建姓名列
clinical_df['姓名'] = ''

# 按组别处理
for group in ['对照组', '观察A组', '观察B组']:
    group_data = clinical_df[clinical_df['分组'] == group].copy()
    print(f"\n处理{group}({len(group_data)}例):")
    
    # 按性别分别处理
    for gender in ['男', '女']:
        gender_data = group_data[group_data['性别'] == gender]
        if len(gender_data) == 0:
            continue
            
        print(f"  {gender}性患者: {len(gender_data)}例")
        
        # 获取对应的姓名池
        pool_key = f"{group}_{gender}"
        available_names = name_pools.get(pool_key, [])
        
        if len(available_names) == 0:
            # 如果没有对应组别的姓名，使用其他组别的同性别姓名
            for other_group in ['对照组', '观察A组', '观察B组']:
                if other_group != group:
                    other_key = f"{other_group}_{gender}"
                    available_names.extend(name_pools.get(other_key, []))
        
        print(f"    可用姓名: {len(available_names)}个")
        
        if len(available_names) > 0:
            # 根据年龄匹配姓名
            gender_indices = gender_data.index.tolist()
            
            # 按年龄排序患者和姓名
            sorted_patients = sorted([(idx, clinical_df.loc[idx, '年龄']) 
                                    for idx in gender_indices], key=lambda x: x[1])
            sorted_names = sorted(available_names, key=lambda x: x['age'])
            
            # 如果姓名不够，重复使用
            name_count = len(sorted_names)
            for i, (patient_idx, patient_age) in enumerate(sorted_patients):
                # 选择年龄最接近的姓名
                if i < name_count:
                    selected_name = sorted_names[i]['name']
                else:
                    # 如果姓名不够，选择年龄相近的
                    age_diffs = [(abs(name_info['age'] - patient_age), name_info['name']) 
                               for name_info in sorted_names]
                    age_diffs.sort()
                    selected_name = age_diffs[i % name_count][1]
                
                clinical_df.loc[patient_idx, '姓名'] = selected_name
                print(f"    患者{clinical_df.loc[patient_idx, '患者ID']} (年龄{patient_age}) -> {selected_name}")

# 5. 检查结果
print("\n5. 检查匹配结果...")
missing_names = clinical_df[clinical_df['姓名'] == '']
if len(missing_names) > 0:
    print(f"警告: 还有{len(missing_names)}个患者没有分配姓名")
    
    # 为剩余患者随机分配姓名
    all_names = []
    for names_list in name_pools.values():
        all_names.extend([name_info['name'] for name_info in names_list])
    
    if len(all_names) > 0:
        for idx in missing_names.index:
            clinical_df.loc[idx, '姓名'] = random.choice(all_names)
            print(f"    随机分配: {clinical_df.loc[idx, '患者ID']} -> {clinical_df.loc[idx, '姓名']}")

print(f"\n所有患者都已分配姓名: {len(clinical_df[clinical_df['姓名'] != ''])} / {len(clinical_df)}")

# 6. 重新排列列的顺序，将姓名放在患者ID后面
print("\n6. 重新排列数据列...")
columns_order = ['患者ID', '姓名'] + [col for col in clinical_df.columns if col not in ['患者ID', '姓名']]
clinical_df = clinical_df[columns_order]

# 7. 保存结果
print("\n7. 保存结果...")
csv_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_含姓名.csv'
excel_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_含姓名.xlsx'

clinical_df.to_csv(csv_output, index=False, encoding='utf-8-sig')
clinical_df.to_excel(excel_output, index=False)

print(f"CSV文件已保存: {csv_output}")
print(f"Excel文件已保存: {excel_output}")

# 8. 显示最终结果概览
print("\n8. 最终结果概览:")
print(f"数据形状: {clinical_df.shape}")
print(f"列名: {clinical_df.columns.tolist()}")

print("\n按组别统计姓名分配:")
for group in ['对照组', '观察A组', '观察B组']:
    group_data = clinical_df[clinical_df['分组'] == group]
    print(f"{group}: {len(group_data)}例")
    for gender in ['男', '女']:
        gender_count = len(group_data[group_data['性别'] == gender])
        if gender_count > 0:
            print(f"  {gender}性: {gender_count}例")

print("\n前5行数据预览:")
print(clinical_df.head().to_string())

print("\n=== 姓名整合完成 ===")