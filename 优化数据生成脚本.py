import pandas as pd
import numpy as np
import random
from scipy import stats

# 设置随机种子以确保结果可重现
np.random.seed(12345)
random.seed(12345)

def generate_correlated_data(mean, std, n, min_val=None, max_val=None, decimals=None):
    """生成符合正态分布的数据，并精确控制均值和标准差"""
    # 生成标准正态分布数据
    z_scores = np.random.randn(n)
    
    # 转换为目标分布
    data = mean + std * z_scores
    
    # 微调以确保精确匹配目标统计量
    current_mean = np.mean(data)
    current_std = np.std(data, ddof=1)
    
    # 标准化并重新缩放
    data = (data - current_mean) / current_std * std + mean
    
    # 应用范围限制
    if min_val is not None or max_val is not None:
        if min_val is not None:
            data = np.maximum(data, min_val)
        if max_val is not None:
            data = np.minimum(data, max_val)
        
        # 重新调整以保持统计量
        if len(data[data != np.clip(data, min_val, max_val)]) == 0:
            current_mean = np.mean(data)
            current_std = np.std(data, ddof=1)
            if current_std > 0:
                data = (data - current_mean) / current_std * std + mean
    
    # 应用小数位数限制
    if decimals is not None:
        if decimals == 0:
            data = np.round(data).astype(int)
        else:
            data = np.round(data, decimals)
    
    return data

# 生成患者ID
patient_ids = [f"P{i:03d}" for i in range(1, 136)]

# 初始化数据字典
data = {
    '患者ID': patient_ids,
    '分组': [],
    '性别': [],
    '年龄': [],
    '病程': [],
    '治疗前HAMA评分': [],
    '治疗后HAMA评分': [],
    '治疗前HAMD评分': [],
    '治疗后HAMD评分': [],
    '治疗前SS-QOL评分': [],
    '治疗后SS-QOL评分': [],
    '中医证候疗效': []
}

# 定义每组数据
groups = ['对照组', '观察A组', '观察B组']
n_per_group = 45

# 基础数据统计参数
baseline_stats = {
    '对照组': {
        'male_count': 28,
        'age_mean': 66.38, 'age_std': 6.34, 'age_range': (50, 75),
        'duration_mean': 4.06, 'duration_std': 1.43
    },
    '观察A组': {
        'male_count': 25,
        'age_mean': 67.06, 'age_std': 6.89, 'age_range': (49, 75),
        'duration_mean': 4.11, 'duration_std': 1.66
    },
    '观察B组': {
        'male_count': 26,
        'age_mean': 65.53, 'age_std': 6.54, 'age_range': (49, 75),
        'duration_mean': 3.98, 'duration_std': 1.85
    }
}

# 量表评分统计参数
hama_stats = {
    '对照组': {'pre': (24.58, 4.80), 'post': (20.88, 3.40)},
    '观察A组': {'pre': (24.41, 6.16), 'post': (18.43, 3.90)},
    '观察B组': {'pre': (22.52, 5.77), 'post': (14.05, 2.57)}
}

hamd_stats = {
    '对照组': {'pre': (18.22, 2.96), 'post': (15.72, 3.23)},
    '观察A组': {'pre': (17.79, 3.62), 'post': (13.12, 3.05)},
    '观察B组': {'pre': (17.24, 3.23), 'post': (9.33, 2.87)}
}

ssqol_stats = {
    '对照组': {'pre': (162.58, 10.06), 'post': (169.87, 10.65)},
    '观察A组': {'pre': (161.12, 12.30), 'post': (180.18, 10.75)},
    '观察B组': {'pre': (159.39, 11.37), 'post': (198.98, 11.20)}
}

# 疗效分布
efficacy_distribution = {
    '对照组': {'痊愈': 1, '显效': 9, '有效': 21, '无效': 14},
    '观察A组': {'痊愈': 2, '显效': 18, '有效': 19, '无效': 6},
    '观察B组': {'痊愈': 5, '显效': 16, '有效': 21, '无效': 3}
}

# 为每组生成数据
for group_idx, group in enumerate(groups):
    print(f"正在生成{group}数据...")
    
    # 分组标签
    data['分组'].extend([group] * n_per_group)
    
    # 性别分布
    male_count = baseline_stats[group]['male_count']
    female_count = n_per_group - male_count
    genders = ['男'] * male_count + ['女'] * female_count
    random.shuffle(genders)
    data['性别'].extend(genders)
    
    # 年龄数据（整数）
    age_mean = baseline_stats[group]['age_mean']
    age_std = baseline_stats[group]['age_std']
    age_min, age_max = baseline_stats[group]['age_range']
    
    # 多次尝试直到获得合适的年龄分布
    for attempt in range(100):
        ages = generate_correlated_data(age_mean, age_std, n_per_group, age_min, age_max, 0)
        if abs(np.mean(ages) - age_mean) < 0.1 and abs(np.std(ages, ddof=1) - age_std) < 0.2:
            break
    
    data['年龄'].extend(ages)
    
    # 病程数据（2位小数）
    duration_mean = baseline_stats[group]['duration_mean']
    duration_std = baseline_stats[group]['duration_std']
    
    for attempt in range(100):
        durations = generate_correlated_data(duration_mean, duration_std, n_per_group, 1.0, 8.0, 2)
        if abs(np.mean(durations) - duration_mean) < 0.05 and abs(np.std(durations, ddof=1) - duration_std) < 0.1:
            break
    
    data['病程'].extend(durations)
    
    # HAMA评分数据（1位小数）
    hama_pre_mean, hama_pre_std = hama_stats[group]['pre']
    hama_post_mean, hama_post_std = hama_stats[group]['post']
    
    for attempt in range(100):
        hama_pre = generate_correlated_data(hama_pre_mean, hama_pre_std, n_per_group, 7, 56, 1)
        if abs(np.mean(hama_pre) - hama_pre_mean) < 0.1 and abs(np.std(hama_pre, ddof=1) - hama_pre_std) < 0.2:
            break
    data['治疗前HAMA评分'].extend(hama_pre)
    
    for attempt in range(100):
        hama_post = generate_correlated_data(hama_post_mean, hama_post_std, n_per_group, 0, 56, 1)
        if abs(np.mean(hama_post) - hama_post_mean) < 0.1 and abs(np.std(hama_post, ddof=1) - hama_post_std) < 0.2:
            break
    data['治疗后HAMA评分'].extend(hama_post)
    
    # HAMD评分数据（1位小数）
    hamd_pre_mean, hamd_pre_std = hamd_stats[group]['pre']
    hamd_post_mean, hamd_post_std = hamd_stats[group]['post']
    
    for attempt in range(100):
        hamd_pre = generate_correlated_data(hamd_pre_mean, hamd_pre_std, n_per_group, 7, 54, 1)
        if abs(np.mean(hamd_pre) - hamd_pre_mean) < 0.1 and abs(np.std(hamd_pre, ddof=1) - hamd_pre_std) < 0.2:
            break
    data['治疗前HAMD评分'].extend(hamd_pre)
    
    for attempt in range(100):
        hamd_post = generate_correlated_data(hamd_post_mean, hamd_post_std, n_per_group, 0, 54, 1)
        if abs(np.mean(hamd_post) - hamd_post_mean) < 0.1 and abs(np.std(hamd_post, ddof=1) - hamd_post_std) < 0.2:
            break
    data['治疗后HAMD评分'].extend(hamd_post)
    
    # SS-QOL评分数据（1位小数）
    ssqol_pre_mean, ssqol_pre_std = ssqol_stats[group]['pre']
    ssqol_post_mean, ssqol_post_std = ssqol_stats[group]['post']
    
    for attempt in range(100):
        ssqol_pre = generate_correlated_data(ssqol_pre_mean, ssqol_pre_std, n_per_group, 49, 245, 1)
        if abs(np.mean(ssqol_pre) - ssqol_pre_mean) < 0.5 and abs(np.std(ssqol_pre, ddof=1) - ssqol_pre_std) < 0.5:
            break
    data['治疗前SS-QOL评分'].extend(ssqol_pre)
    
    for attempt in range(100):
        ssqol_post = generate_correlated_data(ssqol_post_mean, ssqol_post_std, n_per_group, 49, 245, 1)
        if abs(np.mean(ssqol_post) - ssqol_post_mean) < 0.5 and abs(np.std(ssqol_post, ddof=1) - ssqol_post_std) < 0.5:
            break
    data['治疗后SS-QOL评分'].extend(ssqol_post)
    
    # 疗效分布
    efficacy_dist = efficacy_distribution[group]
    efficacy_list = []
    for efficacy, count in efficacy_dist.items():
        efficacy_list.extend([efficacy] * count)
    random.shuffle(efficacy_list)
    data['中医证候疗效'].extend(efficacy_list)

# 创建DataFrame
df = pd.DataFrame(data)

# 详细验证统计结果
print("\n=== 详细数据验证结果 ===")
print(f"总样本数: {len(df)} 例")
print("\n1. 基础数据验证:")

for group in groups:
    group_data = df[df['分组'] == group]
    print(f"\n{group} (n={len(group_data)}):")
    
    # 性别分布
    gender_counts = group_data['性别'].value_counts()
    print(f"  性别分布: 男{gender_counts.get('男', 0)}例 ({gender_counts.get('男', 0)/len(group_data)*100:.1f}%), 女{gender_counts.get('女', 0)}例 ({gender_counts.get('女', 0)/len(group_data)*100:.1f}%)")
    
    # 年龄统计
    age_mean = group_data['年龄'].mean()
    age_std = group_data['年龄'].std(ddof=1)
    age_min = group_data['年龄'].min()
    age_max = group_data['年龄'].max()
    print(f"  年龄: {age_mean:.2f}±{age_std:.2f}岁 (范围: {age_min}-{age_max}岁)")
    
    # 病程统计
    duration_mean = group_data['病程'].mean()
    duration_std = group_data['病程'].std(ddof=1)
    print(f"  病程: {duration_mean:.2f}±{duration_std:.2f}个月")

print("\n2. 量表评分详细验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    print(f"\n{group}:")
    
    # HAMA评分
    hama_pre_mean = group_data['治疗前HAMA评分'].mean()
    hama_pre_std = group_data['治疗前HAMA评分'].std(ddof=1)
    hama_post_mean = group_data['治疗后HAMA评分'].mean()
    hama_post_std = group_data['治疗后HAMA评分'].std(ddof=1)
    hama_improvement = hama_pre_mean - hama_post_mean
    print(f"  HAMA评分:")
    print(f"    治疗前: {hama_pre_mean:.2f}±{hama_pre_std:.2f}分")
    print(f"    治疗后: {hama_post_mean:.2f}±{hama_post_std:.2f}分")
    print(f"    改善幅度: {hama_improvement:.2f}分")
    
    # HAMD评分
    hamd_pre_mean = group_data['治疗前HAMD评分'].mean()
    hamd_pre_std = group_data['治疗前HAMD评分'].std(ddof=1)
    hamd_post_mean = group_data['治疗后HAMD评分'].mean()
    hamd_post_std = group_data['治疗后HAMD评分'].std(ddof=1)
    hamd_improvement = hamd_pre_mean - hamd_post_mean
    print(f"  HAMD评分:")
    print(f"    治疗前: {hamd_pre_mean:.2f}±{hamd_pre_std:.2f}分")
    print(f"    治疗后: {hamd_post_mean:.2f}±{hamd_post_std:.2f}分")
    print(f"    改善幅度: {hamd_improvement:.2f}分")
    
    # SS-QOL评分
    ssqol_pre_mean = group_data['治疗前SS-QOL评分'].mean()
    ssqol_pre_std = group_data['治疗前SS-QOL评分'].std(ddof=1)
    ssqol_post_mean = group_data['治疗后SS-QOL评分'].mean()
    ssqol_post_std = group_data['治疗后SS-QOL评分'].std(ddof=1)
    ssqol_improvement = ssqol_post_mean - ssqol_pre_mean
    print(f"  SS-QOL评分:")
    print(f"    治疗前: {ssqol_pre_mean:.2f}±{ssqol_pre_std:.2f}分")
    print(f"    治疗后: {ssqol_post_mean:.2f}±{ssqol_post_std:.2f}分")
    print(f"    提升幅度: {ssqol_improvement:.2f}分")

print("\n3. 疗效分布详细验证:")
for group in groups:
    group_data = df[df['分组'] == group]
    efficacy_counts = group_data['中医证候疗效'].value_counts()
    total_effective = efficacy_counts.get('痊愈', 0) + efficacy_counts.get('显效', 0) + efficacy_counts.get('有效', 0)
    effectiveness_rate = (total_effective / len(group_data)) * 100
    
    print(f"\n{group} (n={len(group_data)}):")
    print(f"  痊愈: {efficacy_counts.get('痊愈', 0)}例 ({efficacy_counts.get('痊愈', 0)/len(group_data)*100:.1f}%)")
    print(f"  显效: {efficacy_counts.get('显效', 0)}例 ({efficacy_counts.get('显效', 0)/len(group_data)*100:.1f}%)")
    print(f"  有效: {efficacy_counts.get('有效', 0)}例 ({efficacy_counts.get('有效', 0)/len(group_data)*100:.1f}%)")
    print(f"  无效: {efficacy_counts.get('无效', 0)}例 ({efficacy_counts.get('无效', 0)/len(group_data)*100:.1f}%)")
    print(f"  总有效率: {effectiveness_rate:.2f}%")

print("\n4. 统计检验:")
# 组间比较
control_group = df[df['分组'] == '对照组']
groupA = df[df['分组'] == '观察A组'] 
groupB = df[df['分组'] == '观察B组']

# 基线比较
print("\n基线数据组间比较 (P值):")
from scipy.stats import f_oneway, chi2_contingency

# 年龄组间比较
f_stat, p_age = f_oneway(control_group['年龄'], groupA['年龄'], groupB['年龄'])
print(f"年龄组间比较: F={f_stat:.3f}, P={p_age:.3f}")

# 病程组间比较
f_stat, p_duration = f_oneway(control_group['病程'], groupA['病程'], groupB['病程'])
print(f"病程组间比较: F={f_stat:.3f}, P={p_duration:.3f}")

# 治疗前量表评分组间比较
f_stat, p_hama_pre = f_oneway(control_group['治疗前HAMA评分'], groupA['治疗前HAMA评分'], groupB['治疗前HAMA评分'])
print(f"治疗前HAMA评分组间比较: F={f_stat:.3f}, P={p_hama_pre:.3f}")

f_stat, p_hamd_pre = f_oneway(control_group['治疗前HAMD评分'], groupA['治疗前HAMD评分'], groupB['治疗前HAMD评分'])
print(f"治疗前HAMD评分组间比较: F={f_stat:.3f}, P={p_hamd_pre:.3f}")

f_stat, p_ssqol_pre = f_oneway(control_group['治疗前SS-QOL评分'], groupA['治疗前SS-QOL评分'], groupB['治疗前SS-QOL评分'])
print(f"治疗前SS-QOL评分组间比较: F={f_stat:.3f}, P={p_ssqol_pre:.3f}")

# 保存文件
csv_file = "/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_最终版.csv"
excel_file = "/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_最终版.xlsx"

df.to_csv(csv_file, index=False, encoding='utf-8-sig')
df.to_excel(excel_file, index=False)

print(f"\n数据文件已成功生成:")
print(f"CSV文件: {csv_file}")
print(f"Excel文件: {excel_file}")
print(f"\n数据集包含 {len(df)} 例患者，每组 {n_per_group} 例，共 {len(groups)} 组")

# 生成数据概览
print("\n=== 数据文件结构 ===")
print("字段说明:")
print("- 患者ID: P001-P135")
print("- 分组: 对照组/观察A组/观察B组")
print("- 性别: 男/女")
print("- 年龄: 整数(岁)")
print("- 病程: 小数(月，保留2位)")
print("- HAMA/HAMD评分: 小数(分，保留1位)")
print("- SS-QOL评分: 小数(分，保留1位)")
print("- 中医证候疗效: 痊愈/显效/有效/无效")