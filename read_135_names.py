import pandas as pd
import numpy as np
import random

print("=== 读取135例原始病历数据.xlsx ===")

# 读取135例原始病历数据
try:
    names_df = pd.read_excel('/Users/<USER>/Downloads/冯雷/原始数据/135例原始病历数据.xlsx')
    print(f"成功读取文件，数据形状: {names_df.shape}")
    print("列名:", names_df.columns.tolist())
    print("\n前10行数据:")
    print(names_df.head(10))
    
    # 查找可能的姓名列
    name_columns = []
    for col in names_df.columns:
        col_str = str(col).lower()
        if any(keyword in col_str for keyword in ['姓名', 'name', '名字', '患者']):
            name_columns.append(col)
    
    print(f"\n可能的姓名列: {name_columns}")
    
    # 显示更多行来了解数据结构
    print(f"\n显示前20行数据以了解结构:")
    for i in range(min(20, len(names_df))):
        row_data = names_df.iloc[i].tolist()
        print(f"第{i+1}行: {row_data}")

except Exception as e:
    print(f"读取文件出错: {e}")
    print("尝试读取所有sheet...")
    
    # 尝试读取所有sheet
    try:
        excel_file = pd.ExcelFile('/Users/<USER>/Downloads/冯雷/原始数据/135例原始病历数据.xlsx')
        print(f"发现的sheet: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- Sheet: {sheet_name} ---")
            df = pd.read_excel('/Users/<USER>/Downloads/冯雷/原始数据/135例原始病历数据.xlsx', 
                             sheet_name=sheet_name)
            print(f"形状: {df.shape}")
            print("列名:", df.columns.tolist())
            print("前5行:")
            print(df.head())
    except Exception as e2:
        print(f"读取sheet失败: {e2}")