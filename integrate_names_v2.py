import pandas as pd
import numpy as np
import random
from collections import defaultdict

# 设置随机种子
random.seed(42)
np.random.seed(42)

print("=== 开始处理姓名数据整合 ===")

# 1. 读取120例实验记录.xlsx文件，手动解析数据
print("1. 读取120例实验记录.xlsx...")
names_df = pd.read_excel('/Users/<USER>/Downloads/冯雷/原始数据/120例实验记录.xlsx')

print("原始Excel文件形状:", names_df.shape)
print("前10行:")
for i in range(min(10, len(names_df))):
    print(f"第{i}行:", names_df.iloc[i].tolist())

# 手动提取姓名数据
names_data = []
current_group = None

for idx, row in names_df.iterrows():
    row_data = [str(x).strip() if pd.notna(x) else '' for x in row.tolist()]
    
    # 跳过空行和标题行
    if all(x == '' or x == 'nan' for x in row_data):
        continue
    
    # 检查是否是数据行（第一列是数字）
    try:
        seq_num = int(float(row_data[0])) if row_data[0] and row_data[0] != 'nan' else None
        if seq_num and len(row_data) >= 4:
            name = row_data[1] if len(row_data) > 1 else ''
            gender = row_data[2] if len(row_data) > 2 else ''
            age_str = row_data[3] if len(row_data) > 3 else ''
            
            # 从最后一列获取组别信息
            group = ''
            for i in range(len(row_data)-1, -1, -1):
                if '组' in row_data[i]:
                    group = row_data[i]
                    current_group = group
                    break
            
            # 如果当前行没有组别，使用之前记录的组别
            if not group and current_group:
                group = current_group
            
            try:
                age = int(float(age_str)) if age_str and age_str != 'nan' else 0
                if name and name != 'nan' and age > 0 and gender in ['男', '女']:
                    names_data.append({
                        'seq': seq_num,
                        'name': name,
                        'gender': gender,
                        'age': age,
                        'group': group
                    })
                    print(f"提取数据: {seq_num} {name} {gender} {age}岁 {group}")
            except:
                pass
    except:
        pass

print(f"\n成功提取 {len(names_data)} 条姓名数据")

# 2. 读取脑梗死恢复期患者临床研究原始数据
print("\n2. 读取临床研究数据...")
clinical_df = pd.read_csv('/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_最终版.csv')
print(f"临床数据形状: {clinical_df.shape}")
print("临床数据前5行:")
print(clinical_df.head())

# 3. 按组别和性别分类姓名
print("\n3. 按组别和性别分类姓名...")
name_pools = defaultdict(list)

for name_info in names_data:
    group = name_info['group']
    gender = name_info['gender']
    key = f"{group}_{gender}"
    name_pools[key].append(name_info)

print("姓名池统计:")
for key, names in name_pools.items():
    print(f"  {key}: {len(names)}个姓名")
    if len(names) <= 5:  # 如果数量少，显示所有姓名
        for name_info in names:
            print(f"    {name_info['name']} ({name_info['age']}岁)")

# 4. 创建更完整的姓名池（合并同性别的姓名）
print("\n4. 创建性别分类的姓名池...")
gender_pools = {'男': [], '女': []}

for name_info in names_data:
    gender_pools[name_info['gender']].append(name_info)

print(f"男性姓名池: {len(gender_pools['男'])}个")
print(f"女性姓名池: {len(gender_pools['女'])}个")

# 5. 为临床研究数据匹配姓名
print("\n5. 开始为临床数据匹配姓名...")

# 创建姓名列
clinical_df['姓名'] = ''

# 按组别处理
for group in ['对照组', '观察A组', '观察B组']:
    group_data = clinical_df[clinical_df['分组'] == group].copy()
    print(f"\n处理{group} ({len(group_data)}例):")
    
    # 按性别分别处理
    for gender in ['男', '女']:
        gender_data = group_data[group_data['性别'] == gender]
        if len(gender_data) == 0:
            continue
            
        print(f"  {gender}性患者: {len(gender_data)}例")
        
        # 使用性别对应的姓名池
        available_names = gender_pools[gender].copy()
        print(f"    可用{gender}性姓名: {len(available_names)}个")
        
        if len(available_names) > 0:
            # 随机打乱姓名池
            random.shuffle(available_names)
            
            # 为每个患者分配姓名
            gender_indices = gender_data.index.tolist()
            
            for i, patient_idx in enumerate(gender_indices):
                # 循环使用姓名池
                name_idx = i % len(available_names)
                selected_name = available_names[name_idx]['name']
                
                clinical_df.loc[patient_idx, '姓名'] = selected_name
                patient_age = clinical_df.loc[patient_idx, '年龄']
                print(f"    {clinical_df.loc[patient_idx, '患者ID']} (年龄{patient_age}) -> {selected_name}")

# 6. 检查结果
print("\n6. 检查匹配结果...")
missing_names = clinical_df[clinical_df['姓名'] == '']
print(f"未分配姓名的患者: {len(missing_names)}个")

if len(missing_names) > 0:
    # 为剩余患者随机分配姓名
    all_names = [info['name'] for info in names_data]
    for idx in missing_names.index:
        clinical_df.loc[idx, '姓名'] = random.choice(all_names)
        print(f"    随机分配: {clinical_df.loc[idx, '患者ID']} -> {clinical_df.loc[idx, '姓名']}")

print(f"\n所有患者都已分配姓名: {len(clinical_df[clinical_df['姓名'] != ''])} / {len(clinical_df)}")

# 7. 重新排列列的顺序，将姓名放在患者ID后面
print("\n7. 重新排列数据列...")
columns_order = ['患者ID', '姓名'] + [col for col in clinical_df.columns if col not in ['患者ID', '姓名']]
clinical_df = clinical_df[columns_order]

# 8. 保存结果
print("\n8. 保存结果...")
csv_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_含姓名.csv'
excel_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_含姓名.xlsx'

clinical_df.to_csv(csv_output, index=False, encoding='utf-8-sig')
clinical_df.to_excel(excel_output, index=False)

print(f"CSV文件已保存: {csv_output}")
print(f"Excel文件已保存: {excel_output}")

# 9. 显示最终结果概览
print("\n9. 最终结果概览:")
print(f"数据形状: {clinical_df.shape}")
print(f"列名: {clinical_df.columns.tolist()}")

print("\n按组别统计姓名分配:")
for group in ['对照组', '观察A组', '观察B组']:
    group_data = clinical_df[clinical_df['分组'] == group]
    print(f"{group}: {len(group_data)}例")
    for gender in ['男', '女']:
        gender_count = len(group_data[group_data['性别'] == gender])
        if gender_count > 0:
            print(f"  {gender}性: {gender_count}例")

print("\n前5行数据预览:")
print(clinical_df[['患者ID', '姓名', '分组', '性别', '年龄']].head().to_string())

print("\n=== 姓名整合完成 ===")