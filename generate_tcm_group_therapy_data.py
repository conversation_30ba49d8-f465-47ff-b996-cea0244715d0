import pandas as pd
import numpy as np
import random
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子以确保结果可重现
np.random.seed(20240101)
random.seed(20240101)

print("=== 生成脑梗死恢复期患者团体心理护理研究原始数据集 ===")

def generate_precise_normal_data(mean, std, n, min_val=None, max_val=None, decimals=2, integer=False):
    """生成符合指定均值和标准差的正态分布数据"""
    # 初始生成
    data = np.random.normal(mean, std, n)
    
    # 应用范围限制
    if min_val is not None:
        data = np.maximum(data, min_val)
    if max_val is not None:
        data = np.minimum(data, max_val)
    
    # 精确调整到目标统计量（多次迭代优化）
    for iteration in range(10):
        current_mean = np.mean(data)
        current_std = np.std(data, ddof=1)
        
        # 如果已经足够接近，停止调整
        if abs(current_mean - mean) < 0.01 and abs(current_std - std) < 0.01:
            break
            
        if current_std > 0:
            # 标准化并重新缩放
            data = (data - current_mean) / current_std * std + mean
            
            # 重新应用范围限制
            if min_val is not None:
                data = np.maximum(data, min_val)
            if max_val is not None:
                data = np.minimum(data, max_val)
    
    # 应用格式要求
    if integer:
        data = np.round(data).astype(int)
    else:
        data = np.round(data, decimals)
    
    return data

def generate_chinese_names(n):
    """生成中文姓名"""
    surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', 
               '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '柴', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕']
    given_names_male = ['伟', '强', '明', '军', '华', '建', '国', '平', '志', '勇', '杰', '峰', '涛', '磊', '龙', '超', '辉', '鹏', '飞', '东',
                       '海', '山', '天', '林', '森', '成', '达', '康', '健', '安', '宏', '文', '武', '德', '义', '礼', '智', '信', '忠', '孝']
    given_names_female = ['芳', '娟', '敏', '静', '丽', '华', '秀', '英', '红', '玉', '梅', '琴', '燕', '霞', '萍', '兰', '花', '莲', '雪', '云',
                         '月', '星', '晨', '春', '夏', '秋', '冬', '美', '佳', '慧', '聪', '颖', '雅', '馨', '洁', '纯', '真', '善', '爱', '心']
    
    names = []
    for i in range(n):
        surname = random.choice(surnames)
        # 随机选择单字名或双字名
        if random.random() < 0.7:  # 70%概率生成双字名
            if i % 2 == 0:  # 假设偶数为女性倾向
                given_name = random.choice(given_names_female) + random.choice(given_names_female)
            else:
                given_name = random.choice(given_names_male) + random.choice(given_names_male)
        else:  # 30%概率生成单字名
            if i % 2 == 0:
                given_name = random.choice(given_names_female)
            else:
                given_name = random.choice(given_names_male)
        names.append(surname + given_name)
    
    # 确保姓名唯一性
    unique_names = list(set(names))
    while len(unique_names) < n:
        surname = random.choice(surnames)
        given_name = random.choice(given_names_male + given_names_female)
        name = surname + given_name
        if name not in unique_names:
            unique_names.append(name)
    
    return unique_names[:n]

# 初始化数据字典
data = {
    'Patient_ID': [f'P{i:03d}' for i in range(1, 121)],
    'Name': [],
    'Group': [],
    'Gender': [],
    'Age': [],
    'Disease_Duration_Months': [],
    'TCM_Efficacy': [],
    'NIHSS_Pre': [],
    'NIHSS_Post': [],
    'BI_Pre': [],
    'BI_Post': [],
    'Adverse_Reaction': []
}

# 定义研究参数
groups = ['对照组', '观察A组', '观察B组']
n_per_group = 40

# 基线数据统计参数
baseline_params = {
    '对照组': {
        'age_mean': 69.21, 'age_std': 2.83, 'age_range': (56, 75),
        'male_count': 25,
        'duration_mean': 3.45, 'duration_std': 1.26
    },
    '观察A组': {
        'age_mean': 68.54, 'age_std': 2.72, 'age_range': (51, 74),
        'male_count': 23,
        'duration_mean': 3.09, 'duration_std': 1.39
    },
    '观察B组': {
        'age_mean': 69.21, 'age_std': 2.95, 'age_range': (49, 75),
        'male_count': 26,
        'duration_mean': 3.51, 'duration_std': 1.18
    }
}

# NIHSS评分参数
nihss_params = {
    '对照组': {'pre': (12.46, 2.89), 'post': (10.45, 2.71)},
    '观察A组': {'pre': (11.98, 3.58), 'post': (8.46, 2.53)},
    '观察B组': {'pre': (11.40, 3.26), 'post': (6.47, 2.35)}
}

# BI评分参数
bi_params = {
    '对照组': {'pre': (56.41, 5.95), 'post': (63.40, 5.86)},
    '观察A组': {'pre': (55.44, 7.35), 'post': (69.01, 6.40)},
    '观察B组': {'pre': (54.23, 6.69), 'post': (74.69, 6.58)}
}

# 疗效分布
efficacy_distribution = {
    '对照组': {'痊愈': 0, '显效': 9, '有效': 20, '无效': 11},
    '观察A组': {'痊愈': 2, '显效': 17, '有效': 18, '无效': 3},
    '观察B组': {'痊愈': 4, '显效': 16, '有效': 18, '无效': 2}
}

print("开始生成各组数据...")

# 生成中文姓名
all_names = generate_chinese_names(120)
name_idx = 0

# 为每组生成数据
for group_idx, group in enumerate(groups):
    print(f"\n正在生成{group}数据...")
    
    # 分组标签
    data['Group'].extend([group] * n_per_group)
    
    # 姓名分配
    data['Name'].extend(all_names[name_idx:name_idx + n_per_group])
    name_idx += n_per_group
    
    # 性别分布
    male_count = baseline_params[group]['male_count']
    female_count = n_per_group - male_count
    genders = ['男'] * male_count + ['女'] * female_count
    random.shuffle(genders)
    data['Gender'].extend(genders)
    
    # 年龄数据（整数）
    age_mean = baseline_params[group]['age_mean']
    age_std = baseline_params[group]['age_std']
    age_min, age_max = baseline_params[group]['age_range']
    
    ages = generate_precise_normal_data(age_mean, age_std, n_per_group, age_min, age_max, integer=True)
    data['Age'].extend(ages)
    
    # 病程数据
    duration_mean = baseline_params[group]['duration_mean']
    duration_std = baseline_params[group]['duration_std']
    
    durations = generate_precise_normal_data(duration_mean, duration_std, n_per_group, 1.0, 8.0, decimals=2)
    data['Disease_Duration_Months'].extend(durations)
    
    # NIHSS评分数据
    nihss_pre_mean, nihss_pre_std = nihss_params[group]['pre']
    nihss_post_mean, nihss_post_std = nihss_params[group]['post']
    
    nihss_pre = generate_precise_normal_data(nihss_pre_mean, nihss_pre_std, n_per_group, 0, 42, decimals=2)
    data['NIHSS_Pre'].extend(nihss_pre)
    
    nihss_post = generate_precise_normal_data(nihss_post_mean, nihss_post_std, n_per_group, 0, 42, decimals=2)
    data['NIHSS_Post'].extend(nihss_post)
    
    # BI评分数据
    bi_pre_mean, bi_pre_std = bi_params[group]['pre']
    bi_post_mean, bi_post_std = bi_params[group]['post']
    
    bi_pre = generate_precise_normal_data(bi_pre_mean, bi_pre_std, n_per_group, 0, 100, decimals=2)
    data['BI_Pre'].extend(bi_pre)
    
    bi_post = generate_precise_normal_data(bi_post_mean, bi_post_std, n_per_group, 0, 100, decimals=2)
    data['BI_Post'].extend(bi_post)
    
    # 疗效分布
    efficacy_dist = efficacy_distribution[group]
    efficacy_list = []
    for efficacy, count in efficacy_dist.items():
        efficacy_list.extend([efficacy] * count)
    random.shuffle(efficacy_list)
    data['TCM_Efficacy'].extend(efficacy_list)
    
    # 不良反应数据
    if group == '对照组':
        # 对照组有1例轻微头晕
        adverse_reactions = ['无'] * (n_per_group - 1) + ['轻微头晕']
        random.shuffle(adverse_reactions)
        data['Adverse_Reaction'].extend(adverse_reactions)
    else:
        # 其他组无不良反应
        data['Adverse_Reaction'].extend(['无'] * n_per_group)

# 创建DataFrame
df = pd.DataFrame(data)

# 数据验证
print("\n=== 数据验证结果 ===")

print("\n1. 基线数据验证:")
for group in groups:
    group_data = df[df['Group'] == group]
    print(f"\n{group} (n={len(group_data)}):")
    
    # 性别分布
    gender_counts = group_data['Gender'].value_counts()
    print(f"  性别: 男{gender_counts.get('男', 0)}例, 女{gender_counts.get('女', 0)}例")
    
    # 年龄统计
    age_mean = group_data['Age'].mean()
    age_std = group_data['Age'].std(ddof=1)
    age_min = group_data['Age'].min()
    age_max = group_data['Age'].max()
    print(f"  年龄: {age_mean:.2f}±{age_std:.2f}岁 (范围: {age_min}-{age_max}岁)")
    
    # 病程统计
    duration_mean = group_data['Disease_Duration_Months'].mean()
    duration_std = group_data['Disease_Duration_Months'].std(ddof=1)
    print(f"  病程: {duration_mean:.2f}±{duration_std:.2f}个月")

print("\n2. NIHSS评分验证:")
for group in groups:
    group_data = df[df['Group'] == group]
    
    nihss_pre_mean = group_data['NIHSS_Pre'].mean()
    nihss_pre_std = group_data['NIHSS_Pre'].std(ddof=1)
    nihss_post_mean = group_data['NIHSS_Post'].mean()
    nihss_post_std = group_data['NIHSS_Post'].std(ddof=1)
    
    print(f"\n{group}:")
    print(f"  NIHSS - 治疗前: {nihss_pre_mean:.2f}±{nihss_pre_std:.2f}")
    print(f"  NIHSS - 治疗后: {nihss_post_mean:.2f}±{nihss_post_std:.2f}")

print("\n3. BI评分验证:")
for group in groups:
    group_data = df[df['Group'] == group]
    
    bi_pre_mean = group_data['BI_Pre'].mean()
    bi_pre_std = group_data['BI_Pre'].std(ddof=1)
    bi_post_mean = group_data['BI_Post'].mean()
    bi_post_std = group_data['BI_Post'].std(ddof=1)
    
    print(f"\n{group}:")
    print(f"  BI - 治疗前: {bi_pre_mean:.2f}±{bi_pre_std:.2f}")
    print(f"  BI - 治疗后: {bi_post_mean:.2f}±{bi_post_std:.2f}")

print("\n4. 中医证候疗效验证:")
for group in groups:
    group_data = df[df['Group'] == group]
    efficacy_counts = group_data['TCM_Efficacy'].value_counts()
    total_effective = efficacy_counts.get('痊愈', 0) + efficacy_counts.get('显效', 0) + efficacy_counts.get('有效', 0)
    effectiveness_rate = (total_effective / len(group_data)) * 100
    
    print(f"\n{group}:")
    print(f"  痊愈: {efficacy_counts.get('痊愈', 0)}例, 显效: {efficacy_counts.get('显效', 0)}例")
    print(f"  有效: {efficacy_counts.get('有效', 0)}例, 无效: {efficacy_counts.get('无效', 0)}例")
    print(f"  总有效率: {effectiveness_rate:.2f}%")

print("\n5. 不良反应验证:")
adverse_counts = df['Adverse_Reaction'].value_counts()
print(f"无不良反应: {adverse_counts.get('无', 0)}例")
print(f"轻微头晕: {adverse_counts.get('轻微头晕', 0)}例")

# 统计学检验
print("\n=== 统计学检验 ===")

# 基线数据组间比较
ages = [df[df['Group'] == group]['Age'] for group in groups]
f_stat, p_age = stats.f_oneway(*ages)
print(f"\n年龄组间比较: F={f_stat:.3f}, P={p_age:.3f}")

durations = [df[df['Group'] == group]['Disease_Duration_Months'] for group in groups]
f_stat, p_duration = stats.f_oneway(*durations)
print(f"病程组间比较: F={f_stat:.3f}, P={p_duration:.3f}")

# 性别分布卡方检验
from scipy.stats import chi2_contingency
gender_crosstab = pd.crosstab(df['Group'], df['Gender'])
chi2, p_gender, dof, expected = chi2_contingency(gender_crosstab)
print(f"性别分布组间比较: χ²={chi2:.3f}, P={p_gender:.3f}")

# 治疗后NIHSS评分组间比较
nihss_post_values = [df[df['Group'] == group]['NIHSS_Post'] for group in groups]
f_stat, p_nihss = stats.f_oneway(*nihss_post_values)
print(f"\n治疗后NIHSS评分组间比较: F={f_stat:.3f}, P={p_nihss:.3f}")

# 治疗后BI评分组间比较
bi_post_values = [df[df['Group'] == group]['BI_Post'] for group in groups]
f_stat, p_bi = stats.f_oneway(*bi_post_values)
print(f"治疗后BI评分组间比较: F={f_stat:.3f}, P={p_bi:.3f}")

# 疗效分布卡方检验
efficacy_crosstab = pd.crosstab(df['Group'], df['TCM_Efficacy'])
chi2, p_efficacy, dof, expected = chi2_contingency(efficacy_crosstab)
print(f"\n中医证候疗效组间比较: χ²={chi2:.3f}, P={p_efficacy:.3f}")

# 保存数据
output_file = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者血流动力学炎症因子研究原始数据_含姓名.csv'
df.to_csv(output_file, index=False, encoding='utf-8-sig')

print(f"\n数据文件已保存: {output_file}")
print(f"总计生成 {len(df)} 例患者数据，包含 {len(df.columns)} 个字段")

print("\n=== 数据字段说明 ===")
print("Patient_ID: 患者编号 (P001-P120)")
print("Name: 患者姓名 (中文虚拟姓名)")
print("Group: 分组 (对照组/观察A组/观察B组)")
print("Gender: 性别 (男/女)")
print("Age: 年龄 (整数，岁)")
print("Disease_Duration_Months: 病程 (月)")
print("TCM_Efficacy: 中医证候疗效 (痊愈/显效/有效/无效)")
print("NIHSS_Pre/Post: 美国国立卫生院卒中量表评分 (治疗前后)")
print("BI_Pre/Post: 日常生活能力量表评分 (治疗前后)")
print("Adverse_Reaction: 不良反应 (无/轻微头晕)")

print("\n=== 数据生成完成 ===")