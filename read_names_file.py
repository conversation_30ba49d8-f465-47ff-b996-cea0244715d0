import pandas as pd
import numpy as np

# 读取120例实验记录.xlsx文件
names_df = pd.read_excel('/Users/<USER>/Downloads/冯雷/原始数据/120例实验记录.xlsx')

print("120例实验记录.xlsx 文件内容：")
print("文件形状:", names_df.shape)
print("\n列名:", names_df.columns.tolist())
print("\n前10行数据:")
print(names_df.head(10))
print("\n数据类型:")
print(names_df.dtypes)

# 如果有年龄字段，显示年龄分布
if '年龄' in names_df.columns:
    print("\n年龄分布:")
    print(names_df['年龄'].describe())
elif 'Age' in names_df.columns:
    print("\n年龄分布:")
    print(names_df['Age'].describe())

# 检查是否有姓名字段
name_columns = []
for col in names_df.columns:
    if any(keyword in col.lower() for keyword in ['姓名', 'name', '名字', '患者']):
        name_columns.append(col)

print(f"\n可能的姓名字段: {name_columns}")

# 显示所有数据
print("\n完整数据:")
print(names_df.to_string())