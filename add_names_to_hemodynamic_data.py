import pandas as pd
import numpy as np
import random
from collections import defaultdict

print("=== 将姓名添加到血流动力学研究数据中 ===")

# 设置随机种子
random.seed(42)
np.random.seed(42)

# 1. 读取120例实验记录.xlsx文件，提取姓名信息
print("1. 读取120例实验记录.xlsx...")
names_df = pd.read_excel('/Users/<USER>/Downloads/冯雷/原始数据/120例实验记录.xlsx')

print("原始Excel文件形状:", names_df.shape)

# 手动提取姓名数据
names_data = []
current_group = None

for idx, row in names_df.iterrows():
    row_data = [str(x).strip() if pd.notna(x) else '' for x in row.tolist()]
    
    # 跳过空行和标题行
    if all(x == '' or x == 'nan' for x in row_data):
        continue
    
    # 检查是否是数据行（第一列是数字）
    try:
        seq_num = int(float(row_data[0])) if row_data[0] and row_data[0] != 'nan' else None
        if seq_num and len(row_data) >= 4:
            name = row_data[1] if len(row_data) > 1 else ''
            gender = row_data[2] if len(row_data) > 2 else ''
            age_str = row_data[3] if len(row_data) > 3 else ''
            
            # 从最后一列获取组别信息
            group = ''
            for i in range(len(row_data)-1, -1, -1):
                if '组' in row_data[i]:
                    group = row_data[i]
                    current_group = group
                    break
            
            # 如果当前行没有组别，使用之前记录的组别
            if not group and current_group:
                group = current_group
            
            try:
                age = int(float(age_str)) if age_str and age_str != 'nan' else 0
                if name and name != 'nan' and age > 0 and gender in ['男', '女']:
                    names_data.append({
                        'seq': seq_num,
                        'name': name,
                        'gender': gender,
                        'age': age,
                        'group': group
                    })
                    print(f"提取姓名: {seq_num} {name} {gender} {age}岁 {group}")
            except:
                pass
    except:
        pass

print(f"\n成功提取 {len(names_data)} 个姓名")

# 2. 读取血流动力学研究原始数据
print("\n2. 读取血流动力学研究原始数据...")
hemodynamic_df = pd.read_csv('/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者血流动力学炎症因子研究原始数据.csv')
print(f"血流动力学数据形状: {hemodynamic_df.shape}")

# 3. 按性别分类姓名池
print("\n3. 按性别分类姓名...")
gender_name_pools = {'男': [], '女': []}

for name_info in names_data:
    gender_name_pools[name_info['gender']].append(name_info)

print(f"男性姓名池: {len(gender_name_pools['男'])}个")
print(f"女性姓名池: {len(gender_name_pools['女'])}个")

# 4. 为血流动力学数据添加姓名
print("\n4. 为血流动力学数据添加姓名...")

# 在患者ID后面插入姓名列
hemodynamic_df.insert(1, '姓名', '')

# 随机打乱姓名池
random.shuffle(gender_name_pools['男'])
random.shuffle(gender_name_pools['女'])

# 按组别和性别分配姓名
male_name_idx = 0
female_name_idx = 0

for group in ['对照组', '观察A组', '观察B组']:
    group_data = hemodynamic_df[hemodynamic_df['分组'] == group]
    print(f"\n处理{group} ({len(group_data)}例):")
    
    for gender in ['男', '女']:
        gender_patients = group_data[group_data['性别'] == gender]
        if len(gender_patients) == 0:
            continue
            
        print(f"  {gender}性患者: {len(gender_patients)}例")
        
        # 为当前性别的患者分配姓名
        for patient_idx in gender_patients.index:
            if gender == '男' and male_name_idx < len(gender_name_pools['男']):
                selected_name = gender_name_pools['男'][male_name_idx]['name']
                hemodynamic_df.loc[patient_idx, '姓名'] = selected_name
                patient_id = hemodynamic_df.loc[patient_idx, '患者ID']
                patient_age = hemodynamic_df.loc[patient_idx, '年龄']
                print(f"    {patient_id} (年龄{patient_age}) -> {selected_name}")
                male_name_idx += 1
            elif gender == '女' and female_name_idx < len(gender_name_pools['女']):
                selected_name = gender_name_pools['女'][female_name_idx]['name']
                hemodynamic_df.loc[patient_idx, '姓名'] = selected_name
                patient_id = hemodynamic_df.loc[patient_idx, '患者ID']
                patient_age = hemodynamic_df.loc[patient_idx, '年龄']
                print(f"    {patient_id} (年龄{patient_age}) -> {selected_name}")
                female_name_idx += 1

# 5. 处理可能剩余的未分配患者
print("\n5. 检查未分配姓名的患者...")
missing_names = hemodynamic_df[hemodynamic_df['姓名'] == '']
if len(missing_names) > 0:
    print(f"发现 {len(missing_names)} 个未分配姓名的患者")
    
    # 循环使用已有姓名
    all_names = []
    for gender_pool in gender_name_pools.values():
        all_names.extend([name_info['name'] for name_info in gender_pool])
    
    if all_names:
        for i, idx in enumerate(missing_names.index):
            gender = hemodynamic_df.loc[idx, '性别']
            # 优先使用同性别姓名
            suitable_names = [name for name_info in gender_name_pools[gender] for name in [name_info['name']]]
            if suitable_names:
                selected_name = suitable_names[i % len(suitable_names)]
            else:
                selected_name = all_names[i % len(all_names)]
            
            hemodynamic_df.loc[idx, '姓名'] = selected_name
            patient_id = hemodynamic_df.loc[idx, '患者ID']
            print(f"  补充分配: {patient_id} -> {selected_name}")

print(f"\n所有患者姓名分配完成: {len(hemodynamic_df[hemodynamic_df['姓名'] != ''])} / {len(hemodynamic_df)}")

# 6. 保存包含姓名的完整数据
print("\n6. 保存包含姓名的完整数据...")
csv_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者血流动力学炎症因子研究原始数据_含姓名.csv'
excel_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者血流动力学炎症因子研究原始数据_含姓名.xlsx'

hemodynamic_df.to_csv(csv_output, index=False, encoding='utf-8-sig')
hemodynamic_df.to_excel(excel_output, index=False)

print(f"CSV文件已保存: {csv_output}")
print(f"Excel文件已保存: {excel_output}")

# 7. 验证最终结果
print("\n7. 最终结果验证:")
print(f"数据形状: {hemodynamic_df.shape}")
print("列名:", hemodynamic_df.columns.tolist())

print("\n按组别统计:")
for group in ['对照组', '观察A组', '观察B组']:
    group_data = hemodynamic_df[hemodynamic_df['分组'] == group]
    print(f"{group}: {len(group_data)}例")
    for gender in ['男', '女']:
        gender_count = len(group_data[group_data['性别'] == gender])
        if gender_count > 0:
            print(f"  {gender}性: {gender_count}例")

print("\n前10行数据预览:")
display_columns = ['患者ID', '姓名', '分组', '性别', '年龄', '病程时间', 'BA_pre', 'BA_post', '临床疗效']
print(hemodynamic_df[display_columns].head(10).to_string())

print("\n=== 姓名整合完成 ===")