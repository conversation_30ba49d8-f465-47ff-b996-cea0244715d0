import pandas as pd
import numpy as np
import random

print("=== 根据135例原始病历数据重新匹配姓名 ===")

# 1. 读取135例原始病历数据，提取姓名信息
print("1. 提取135例原始病历数据中的姓名...")
names_df = pd.read_excel('/Users/<USER>/Downloads/冯雷/原始数据/135例原始病历数据.xlsx')

# 找到真正的数据起始行（序号行之后的第一个数字行）
names_data = []
start_extracting = False

for idx, row in names_df.iterrows():
    row_data = [x if pd.notna(x) else '' for x in row.tolist()]
    
    # 找到标题行
    if len(row_data) > 1 and str(row_data[0]).strip() == '序号' and str(row_data[1]).strip() == '姓名':
        start_extracting = True
        print(f"找到标题行在第{idx+1}行")
        continue
    
    # 开始提取数据
    if start_extracting:
        try:
            # 检查第一列是否为数字（序号）
            seq_str = str(row_data[0]).strip()
            if seq_str and seq_str.replace('.', '').replace('-', '').isdigit():
                seq_num = int(float(seq_str))
                name = str(row_data[1]).strip() if len(row_data) > 1 else ''
                gender = str(row_data[2]).strip() if len(row_data) > 2 else ''
                age_str = str(row_data[3]).strip() if len(row_data) > 3 else ''
                
                # 尝试解析年龄
                age = 0
                try:
                    age = int(float(age_str)) if age_str and age_str != 'nan' else 0
                except:
                    pass
                
                # 只提取有效数据
                if name and name != 'nan' and gender in ['男', '女'] and age > 0:
                    names_data.append({
                        'seq': seq_num,
                        'name': name,
                        'gender': gender,
                        'age': age
                    })
                    print(f"提取: {seq_num} {name} {gender} {age}岁")
                    
                    # 如果已经提取到135例，停止
                    if len(names_data) >= 135:
                        break
        except:
            continue

print(f"\n成功提取 {len(names_data)} 个姓名")

if len(names_data) < 135:
    print(f"警告: 只提取到{len(names_data)}个姓名，少于135个")

# 2. 按性别分类姓名
print("\n2. 按性别分类姓名...")
male_names = [item for item in names_data if item['gender'] == '男']
female_names = [item for item in names_data if item['gender'] == '女']

print(f"男性姓名: {len(male_names)}个")
print(f"女性姓名: {len(female_names)}个")

# 3. 读取现有的临床数据
print("\n3. 读取现有临床数据...")
clinical_df = pd.read_csv('/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_最终版.csv')
print(f"临床数据形状: {clinical_df.shape}")

# 4. 重新分配姓名，保持患者ID和其他数据不变
print("\n4. 重新分配姓名...")

# 创建或更新姓名列
if '姓名' not in clinical_df.columns:
    clinical_df.insert(1, '姓名', '')
else:
    clinical_df['姓名'] = ''

# 设置随机种子以保证结果可重现
random.seed(42)
random.shuffle(male_names)
random.shuffle(female_names)

# 按组别和性别重新分配姓名
male_name_idx = 0
female_name_idx = 0

for group in ['对照组', '观察A组', '观察B组']:
    group_data = clinical_df[clinical_df['分组'] == group]
    print(f"\n处理{group} ({len(group_data)}例):")
    
    for gender in ['男', '女']:
        gender_patients = group_data[group_data['性别'] == gender]
        if len(gender_patients) == 0:
            continue
            
        print(f"  {gender}性患者: {len(gender_patients)}例")
        
        # 为当前性别的患者分配姓名
        for patient_idx in gender_patients.index:
            if gender == '男' and male_name_idx < len(male_names):
                selected_name = male_names[male_name_idx]['name']
                clinical_df.loc[patient_idx, '姓名'] = selected_name
                patient_id = clinical_df.loc[patient_idx, '患者ID']
                patient_age = clinical_df.loc[patient_idx, '年龄']
                print(f"    {patient_id} (年龄{patient_age}) -> {selected_name}")
                male_name_idx += 1
            elif gender == '女' and female_name_idx < len(female_names):
                selected_name = female_names[female_name_idx]['name']
                clinical_df.loc[patient_idx, '姓名'] = selected_name
                patient_id = clinical_df.loc[patient_idx, '患者ID']
                patient_age = clinical_df.loc[patient_idx, '年龄']
                print(f"    {patient_id} (年龄{patient_age}) -> {selected_name}")
                female_name_idx += 1

# 5. 处理可能剩余的未分配患者
print("\n5. 检查未分配姓名的患者...")
missing_names = clinical_df[clinical_df['姓名'] == '']
if len(missing_names) > 0:
    print(f"发现 {len(missing_names)} 个未分配姓名的患者")
    
    # 循环使用已有姓名
    all_names = male_names + female_names
    for i, idx in enumerate(missing_names.index):
        if all_names:
            gender = clinical_df.loc[idx, '性别']
            suitable_names = [n for n in all_names if n['gender'] == gender]
            if suitable_names:
                selected_name = suitable_names[i % len(suitable_names)]['name']
            else:
                selected_name = all_names[i % len(all_names)]['name']
            
            clinical_df.loc[idx, '姓名'] = selected_name
            print(f"  补充分配: {clinical_df.loc[idx, '患者ID']} -> {selected_name}")

print(f"\n所有患者姓名分配完成: {len(clinical_df[clinical_df['姓名'] != ''])} / {len(clinical_df)}")

# 6. 保存更新后的数据
print("\n6. 保存更新后的数据...")
csv_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_正确姓名.csv'
excel_output = '/Users/<USER>/Downloads/冯雷/原始数据/脑梗死恢复期患者临床研究原始数据_正确姓名.xlsx'

clinical_df.to_csv(csv_output, index=False, encoding='utf-8-sig')
clinical_df.to_excel(excel_output, index=False)

print(f"CSV文件已保存: {csv_output}")
print(f"Excel文件已保存: {excel_output}")

# 7. 验证结果
print("\n7. 验证结果...")
print(f"最终数据形状: {clinical_df.shape}")
print("列名:", clinical_df.columns.tolist())

print("\n按组别统计:")
for group in ['对照组', '观察A组', '观察B组']:
    group_data = clinical_df[clinical_df['分组'] == group]
    print(f"{group}: {len(group_data)}例")
    for gender in ['男', '女']:
        gender_count = len(group_data[group_data['性别'] == gender])
        if gender_count > 0:
            print(f"  {gender}性: {gender_count}例")

print("\n前10行数据预览:")
print(clinical_df[['患者ID', '姓名', '分组', '性别', '年龄']].head(10).to_string())

print("\n=== 姓名更新完成 ===")