#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import json

def csv_to_html_table():
    """将CSV转换为HTML表格格式，便于在Excel中打开"""
    
    # 读取CSV数据
    with open('胃癌术后抑郁症研究数据集_精确版.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        data = list(reader)
    
    # 创建HTML表格
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>胃癌术后抑郁症研究数据集</title>
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .control-group { background-color: #e8f4fd; }
        .observation-group { background-color: #fff2cc; }
    </style>
</head>
<body>
    <h1>胃癌术后抑郁症研究数据集</h1>
    <p>研究类型：中西医结合心理疏导治疗胃癌术后抑郁症的随机对照试验</p>
    <p>研究时间：2020年1月至2023年6月</p>
    <p>总样本量：106例患者（对照组53例，观察组53例）</p>
    
    <table>
        <thead>
            <tr>
                <th>患者编号</th>
                <th>姓名</th>
                <th>分组</th>
                <th>性别</th>
                <th>年龄(岁)</th>
                <th>TNM分期</th>
                <th>中医证候疗效</th>
                <th>HAMD-17治疗前</th>
                <th>HAMD-17治疗后</th>
                <th>KPS治疗前</th>
                <th>KPS治疗后</th>
            </tr>
        </thead>
        <tbody>
'''
    
    for row in data:
        css_class = 'control-group' if row['group'] == '对照组' else 'observation-group'
        html_content += f'''            <tr class="{css_class}">
                <td>{row['patient_id']}</td>
                <td>{row['name']}</td>
                <td>{row['group']}</td>
                <td>{row['gender']}</td>
                <td>{row['age']}</td>
                <td>{row['tnm_stage']}</td>
                <td>{row['treatment_efficacy']}</td>
                <td>{row['hamd_pre']}</td>
                <td>{row['hamd_post']}</td>
                <td>{row['kps_pre']}</td>
                <td>{row['kps_post']}</td>
            </tr>
'''
    
    html_content += '''        </tbody>
    </table>
    
    <h2>数据统计摘要</h2>
    <h3>基线特征</h3>
    <ul>
        <li><strong>对照组</strong>：53例（男31例/女22例），年龄63.56±5.89岁（范围55-74岁），TNM分期I期12例/II期33例/III期8例</li>
        <li><strong>观察组</strong>：53例（男33例/女20例），年龄62.91±6.12岁（范围53-71岁），TNM分期I期11例/II期35例/III期7例</li>
    </ul>
    
    <h3>中医证候疗效</h3>
    <ul>
        <li><strong>对照组总有效率</strong>：71.70%（临床痊愈1例，显效17例，有效20例，无效15例）</li>
        <li><strong>观察组总有效率</strong>：90.57%（临床痊愈5例，显效21例，有效22例，无效5例）</li>
        <li><strong>统计学比较</strong>：Χ² = 6.163，P = 0.013</li>
    </ul>
    
    <h3>HAMD-17抑郁量表评分</h3>
    <ul>
        <li><strong>对照组</strong>：治疗前17.55±3.02 → 治疗后14.31±3.51</li>
        <li><strong>观察组</strong>：治疗前17.98±3.35 → 治疗后11.12±3.82</li>
        <li><strong>组间比较</strong>：t=4.476，P<0.001</li>
    </ul>
    
    <h3>KPS功能评分</h3>
    <ul>
        <li><strong>对照组</strong>：治疗前66.19±2.93 → 治疗后71.52±4.72</li>
        <li><strong>观察组</strong>：治疗前65.86±3.06 → 治疗后78.38±4.53</li>
        <li><strong>组间比较</strong>：t=7.634，P<0.001</li>
    </ul>
    
    <h3>治疗方案</h3>
    <ul>
        <li><strong>对照组</strong>：常规治疗+盐酸氟西汀胶囊（20mg，1次/日，早餐后30分钟口服）</li>
        <li><strong>观察组</strong>：对照组治疗+中西医结合心理疏导</li>
    </ul>
</body>
</html>'''
    
    # 保存HTML文件
    with open('胃癌术后抑郁症研究数据集.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("HTML数据表已生成：胃癌术后抑郁症研究数据集.html")
    print("该文件可以直接在Excel中打开，保持完整的格式和样式。")

if __name__ == "__main__":
    csv_to_html_table()