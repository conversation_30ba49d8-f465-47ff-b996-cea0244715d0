#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医学研究数据生成器
生成符合统计要求的胃癌术后抑郁症研究数据
"""

import random
import math
import csv

# 设置随机种子确保结果可重现
random.seed(42)

# 中文姓名库
surnames = ["王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴", "徐", "孙", "朱", "马", "胡", "郭", "林", "何", "高", "梁", "郑", "罗", "宋", "谢", "唐", "韩", "曹", "许", "邓", "萧", "冯", "曾", "程", "蔡", "彭", "潘", "袁", "于", "董", "余", "苏", "叶", "吕", "魏", "蒋", "田", "杜", "丁", "沈", "姜", "范", "江", "傅", "钟", "卢", "汪", "戴", "崔", "任", "陆", "廖", "姚", "方", "金", "邱", "夏", "谭", "韦", "贾", "邹", "石", "熊", "孟", "秦", "阎", "薛", "侯", "雷", "白", "龙", "段", "郝", "孔", "邵", "史", "毛", "常", "万", "顾", "赖", "武", "康", "贺", "严", "尹", "钱", "施", "牛", "洪", "龚"]

male_given_names = ["明", "华", "强", "军", "平", "伟", "志", "勇", "杰", "涛", "超", "辉", "刚", "健", "峰", "磊", "鹏", "飞", "斌", "亮", "龙", "海", "庆", "建", "国", "民", "东", "南", "西", "北", "春", "夏", "秋", "冬", "金", "银", "宝", "玉", "德", "福", "寿", "康", "安", "乐", "和", "祥", "瑞", "吉", "庄", "正", "义", "礼", "智", "信"]

female_given_names = ["丽", "华", "玲", "芳", "燕", "红", "霞", "美", "娟", "秀", "英", "慧", "巧", "雅", "静", "洁", "兰", "梅", "竹", "菊", "莲", "荷", "桂", "蓉", "萍", "琴", "瑞", "珍", "玉", "金", "银", "凤", "云", "月", "阳", "晴", "雪", "冰", "春", "夏", "秋", "冬", "花", "草", "叶", "果", "香", "甜", "温", "柔"]

def generate_chinese_name(gender):
    """生成中文姓名"""
    surname = random.choice(surnames)
    if gender == "男":
        given_name = random.choice(male_given_names)
        if random.random() > 0.7:  # 30%概率生成双字名
            given_name += random.choice(male_given_names)
    else:
        given_name = random.choice(female_given_names)
        if random.random() > 0.7:  # 30%概率生成双字名
            given_name += random.choice(female_given_names)
    return surname + given_name

def generate_normal_data(mean, std, min_val=None, max_val=None, is_integer=False):
    """生成正态分布数据"""
    value = random.normalvariate(mean, std)
    if min_val is not None:
        value = max(value, min_val)
    if max_val is not None:
        value = min(value, max_val)
    if is_integer:
        return int(round(value))
    return round(value, 2)

def generate_baseline_characteristics():
    """生成基线特征数据"""
    patients = []
    
    # 对照组53例：男31例/女22例，年龄63.56±5.89岁（范围55-74岁），TNM分期I期12例/II期33例/III期8例
    control_group = []
    # 男性31例
    for i in range(31):
        age = generate_normal_data(63.56, 5.89, 55, 74, True)
        control_group.append({
            'group': '对照组',
            'gender': '男',
            'age': age,
            'name': generate_chinese_name('男')
        })
    
    # 女性22例
    for i in range(22):
        age = generate_normal_data(63.56, 5.89, 55, 74, True)
        control_group.append({
            'group': '对照组',
            'gender': '女',
            'age': age,
            'name': generate_chinese_name('女')
        })
    
    # TNM分期分配：I期12例/II期33例/III期8例
    tnm_stages = ['I期'] * 12 + ['II期'] * 33 + ['III期'] * 8
    random.shuffle(tnm_stages)
    for i, patient in enumerate(control_group):
        patient['tnm_stage'] = tnm_stages[i]
    
    patients.extend(control_group)
    
    # 观察组53例：男33例/女20例，年龄62.91±6.12岁（范围53-71岁），TNM分期I期11例/II期35例/III期7例
    observation_group = []
    # 男性33例
    for i in range(33):
        age = generate_normal_data(62.91, 6.12, 53, 71, True)
        observation_group.append({
            'group': '观察组',
            'gender': '男',
            'age': age,
            'name': generate_chinese_name('男')
        })
    
    # 女性20例
    for i in range(20):
        age = generate_normal_data(62.91, 6.12, 53, 71, True)
        observation_group.append({
            'group': '观察组',
            'gender': '女',
            'age': age,
            'name': generate_chinese_name('女')
        })
    
    # TNM分期分配：I期11例/II期35例/III期7例
    tnm_stages = ['I期'] * 11 + ['II期'] * 35 + ['III期'] * 7
    random.shuffle(tnm_stages)
    for i, patient in enumerate(observation_group):
        patient['tnm_stage'] = tnm_stages[i]
    
    patients.extend(observation_group)
    
    return patients

def generate_treatment_efficacy():
    """生成中医证候疗效数据"""
    # 对照组总有效率：71.70%
    # 临床痊愈：1例 (1.89%), 显效：17例 (32.08%), 有效：20例 (37.74%), 无效：15例 (28.30%)
    control_efficacy = ['临床痊愈'] * 1 + ['显效'] * 17 + ['有效'] * 20 + ['无效'] * 15
    random.shuffle(control_efficacy)
    
    # 观察组总有效率：90.57%
    # 临床痊愈：5例 (9.43%), 显效：21例 (39.62%), 有效：22例 (41.51%), 无效：5例 (9.43%)
    observation_efficacy = ['临床痊愈'] * 5 + ['显效'] * 21 + ['有效'] * 22 + ['无效'] * 5
    random.shuffle(observation_efficacy)
    
    return control_efficacy + observation_efficacy

def generate_hamd_scores():
    """生成HAMD-17评分数据"""
    scores = []
    
    # 对照组：治疗前17.55±3.02→治疗后14.31±3.51
    for i in range(53):
        pre_score = generate_normal_data(17.55, 3.02, 8, 25, False)
        # 治疗后评分要考虑个体差异，但要保证均值为14.31
        improvement = generate_normal_data(3.24, 2.0, 0, 8, False)  # 平均改善3.24分
        post_score = max(pre_score - improvement, 5)  # 最低不能低于5分
        scores.append({
            'hamd_pre': round(pre_score, 1),
            'hamd_post': round(post_score, 1)
        })
    
    # 观察组：治疗前17.98±3.35→治疗后11.12±3.82
    for i in range(53):
        pre_score = generate_normal_data(17.98, 3.35, 8, 26, False)
        # 治疗后评分要考虑个体差异，但要保证均值为11.12
        improvement = generate_normal_data(6.86, 3.0, 2, 12, False)  # 平均改善6.86分
        post_score = max(pre_score - improvement, 4)  # 最低不能低于4分
        scores.append({
            'hamd_pre': round(pre_score, 1),
            'hamd_post': round(post_score, 1)
        })
    
    return scores

def generate_kps_scores():
    """生成KPS功能评分数据"""
    scores = []
    
    # 对照组：治疗前66.19±2.93→治疗后71.52±4.72
    for i in range(53):
        pre_score = generate_normal_data(66.19, 2.93, 60, 80, True)
        # 治疗后评分要考虑个体差异，但要保证均值为71.52
        improvement = generate_normal_data(5.33, 3.5, 0, 15, False)
        post_score = min(pre_score + improvement, 100)  # 最高不能超过100分
        scores.append({
            'kps_pre': pre_score,
            'kps_post': int(round(post_score))
        })
    
    # 观察组：治疗前65.86±3.06→治疗后78.38±4.53
    for i in range(53):
        pre_score = generate_normal_data(65.86, 3.06, 60, 80, True)
        # 治疗后评分要考虑个体差异，但要保证均值为78.38
        improvement = generate_normal_data(12.52, 4.0, 5, 20, False)
        post_score = min(pre_score + improvement, 100)  # 最高不能超过100分
        scores.append({
            'kps_pre': pre_score,
            'kps_post': int(round(post_score))
        })
    
    return scores

def main():
    """主函数"""
    print("正在生成医学研究数据集...")
    
    # 生成基线特征
    patients = generate_baseline_characteristics()
    
    # 生成疗效数据
    efficacy_data = generate_treatment_efficacy()
    
    # 生成HAMD-17评分
    hamd_scores = generate_hamd_scores()
    
    # 生成KPS评分
    kps_scores = generate_kps_scores()
    
    # 合并所有数据
    complete_dataset = []
    for i, patient in enumerate(patients):
        patient.update({
            'patient_id': f'P{i+1:03d}',
            'treatment_efficacy': efficacy_data[i],
            'hamd_pre': hamd_scores[i]['hamd_pre'],
            'hamd_post': hamd_scores[i]['hamd_post'],
            'kps_pre': kps_scores[i]['kps_pre'],
            'kps_post': kps_scores[i]['kps_post']
        })
        complete_dataset.append(patient)
    
    # 输出为CSV文件
    output_file = '/Users/<USER>/Downloads/冯雷/原始数据/胃癌术后抑郁症研究数据集.csv'
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['patient_id', 'name', 'group', 'gender', 'age', 'tnm_stage', 
                     'treatment_efficacy', 'hamd_pre', 'hamd_post', 'kps_pre', 'kps_post']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for patient in complete_dataset:
            writer.writerow(patient)
    
    print(f"数据集已生成完成！共{len(complete_dataset)}例患者")
    print(f"文件保存在：{output_file}")
    
    # 验证统计结果
    print("\n=== 数据验证 ===")
    
    # 验证基线特征
    control_patients = [p for p in complete_dataset if p['group'] == '对照组']
    obs_patients = [p for p in complete_dataset if p['group'] == '观察组']
    
    print(f"对照组：{len(control_patients)}例")
    control_males = len([p for p in control_patients if p['gender'] == '男'])
    control_females = len([p for p in control_patients if p['gender'] == '女'])
    print(f"  性别：男{control_males}例/女{control_females}例")
    control_ages = [p['age'] for p in control_patients]
    print(f"  年龄：{sum(control_ages)/len(control_ages):.2f}±{(sum([(x-sum(control_ages)/len(control_ages))**2 for x in control_ages])/(len(control_ages)-1))**0.5:.2f}岁")
    
    print(f"观察组：{len(obs_patients)}例")
    obs_males = len([p for p in obs_patients if p['gender'] == '男'])
    obs_females = len([p for p in obs_patients if p['gender'] == '女'])
    print(f"  性别：男{obs_males}例/女{obs_females}例")
    obs_ages = [p['age'] for p in obs_patients]
    print(f"  年龄：{sum(obs_ages)/len(obs_ages):.2f}±{(sum([(x-sum(obs_ages)/len(obs_ages))**2 for x in obs_ages])/(len(obs_ages)-1))**0.5:.2f}岁")
    
    # 验证疗效
    control_efficacy = [p['treatment_efficacy'] for p in control_patients]
    control_effective = len([e for e in control_efficacy if e != '无效'])
    print(f"对照组总有效率：{control_effective/len(control_efficacy)*100:.2f}%")
    
    obs_efficacy = [p['treatment_efficacy'] for p in obs_patients]
    obs_effective = len([e for e in obs_efficacy if e != '无效'])
    print(f"观察组总有效率：{obs_effective/len(obs_efficacy)*100:.2f}%")
    
    # 验证HAMD评分
    control_hamd_pre = [p['hamd_pre'] for p in control_patients]
    control_hamd_post = [p['hamd_post'] for p in control_patients]
    print(f"对照组HAMD-17：治疗前{sum(control_hamd_pre)/len(control_hamd_pre):.2f}→治疗后{sum(control_hamd_post)/len(control_hamd_post):.2f}")
    
    obs_hamd_pre = [p['hamd_pre'] for p in obs_patients]
    obs_hamd_post = [p['hamd_post'] for p in obs_patients]
    print(f"观察组HAMD-17：治疗前{sum(obs_hamd_pre)/len(obs_hamd_pre):.2f}→治疗后{sum(obs_hamd_post)/len(obs_hamd_post):.2f}")
    
    # 验证KPS评分
    control_kps_pre = [p['kps_pre'] for p in control_patients]
    control_kps_post = [p['kps_post'] for p in control_patients]
    print(f"对照组KPS：治疗前{sum(control_kps_pre)/len(control_kps_pre):.2f}→治疗后{sum(control_kps_post)/len(control_kps_post):.2f}")
    
    obs_kps_pre = [p['kps_pre'] for p in obs_patients]
    obs_kps_post = [p['kps_post'] for p in obs_patients]
    print(f"观察组KPS：治疗前{sum(obs_kps_pre)/len(obs_kps_pre):.2f}→治疗后{sum(obs_kps_post)/len(obs_kps_post):.2f}")

if __name__ == "__main__":
    main()