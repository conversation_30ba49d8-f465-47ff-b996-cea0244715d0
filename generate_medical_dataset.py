#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医学研究数据集生成器
生成符合医疗专业标准的中西医结合心理疏导治疗胃癌术后抑郁症随机对照试验数据
"""

import pandas as pd
import numpy as np
import random
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class MedicalDatasetGenerator:
    def __init__(self):
        # 设置随机种子以确保结果可重现
        np.random.seed(42)
        random.seed(42)
        
        # 中国姓名库
        self.surnames = [
            '陈', '王', '李', '张', '刘', '杨', '赵', '黄', '周', '吴',
            '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
            '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧',
            '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕',
            '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎'
        ]
        
        self.male_names = [
            '志强', '建国', '建军', '卫华', '志明', '国强', '志华', '建华',
            '志勇', '国华', '志刚', '建伟', '志伟', '国伟', '志军', '建强',
            '国军', '志国', '建民', '国民', '志民', '志平', '建平', '国平',
            '志龙', '建龙', '国龙', '志峰', '建峰', '国峰', '志文', '建文',
            '国文', '志斌', '建斌', '国斌', '志宏', '建宏', '国宏', '志东'
        ]
        
        self.female_names = [
            '淑珍', '淑华', '淑英', '淑芳', '淑兰', '淑梅', '淑琴', '淑萍',
            '淑娟', '淑霞', '美华', '美英', '美芳', '美兰', '美梅', '美琴',
            '美萍', '美娟', '美霞', '美玲', '玉华', '玉英', '玉芳', '玉兰',
            '玉梅', '玉琴', '玉萍', '玉娟', '玉霞', '玉玲', '桂华', '桂英',
            '桂芳', '桂兰', '桂梅', '桂琴', '桂萍', '桂娟', '桂霞', '桂玲'
        ]
        
    def generate_name(self, gender):
        """生成中国姓名"""
        surname = random.choice(self.surnames)
        if gender == '男':
            given_name = random.choice(self.male_names)
        else:
            given_name = random.choice(self.female_names)
        return surname + given_name
    
    def generate_baseline_characteristics(self):
        """生成基线特征数据"""
        patients = []
        
        # 对照组53例
        control_group = []
        # 男31例，女22例
        for i in range(31):  # 男性
            age = int(np.random.normal(63.56, 5.89))
            age = max(55, min(74, age))  # 限制在55-74岁范围
            control_group.append({
                '患者ID': f'C{i+1:03d}',
                '姓名': self.generate_name('男'),
                '性别': '男',
                '年龄': age,
                '分组': '对照组'
            })
            
        for i in range(22):  # 女性
            age = int(np.random.normal(63.56, 5.89))
            age = max(55, min(74, age))
            control_group.append({
                '患者ID': f'C{i+32:03d}',
                '姓名': self.generate_name('女'),
                '性别': '女',
                '年龄': age,
                '分组': '对照组'
            })
        
        # TNM分期分配：I期12例，II期33例，III期8例
        stages = ['I期'] * 12 + ['II期'] * 33 + ['III期'] * 8
        random.shuffle(stages)
        for i, patient in enumerate(control_group):
            patient['TNM分期'] = stages[i]
        
        # 观察组53例
        observation_group = []
        # 男33例，女20例
        for i in range(33):  # 男性
            age = int(np.random.normal(62.91, 6.12))
            age = max(53, min(71, age))  # 限制在53-71岁范围
            observation_group.append({
                '患者ID': f'O{i+1:03d}',
                '姓名': self.generate_name('男'),
                '性别': '男',
                '年龄': age,
                '分组': '观察组'
            })
            
        for i in range(20):  # 女性
            age = int(np.random.normal(62.91, 6.12))
            age = max(53, min(71, age))
            observation_group.append({
                '患者ID': f'O{i+34:03d}',
                '姓名': self.generate_name('女'),
                '性别': '女',
                '年龄': age,
                '分组': '观察组'
            })
        
        # TNM分期分配：I期11例，II期35例，III期7例
        stages = ['I期'] * 11 + ['II期'] * 35 + ['III期'] * 7
        random.shuffle(stages)
        for i, patient in enumerate(observation_group):
            patient['TNM分期'] = stages[i]
        
        # 合并两组数据
        patients = control_group + observation_group
        
        return patients
    
    def generate_hamd_scores(self, patients):
        """生成HAMD-17评分数据"""
        for patient in patients:
            if patient['分组'] == '对照组':
                # 治疗前：17.55±3.02
                pre_score = np.random.normal(17.55, 3.02)
                pre_score = round(max(7, min(26, pre_score)), 1)  # HAMD-17范围0-52，但抑郁症患者通常7-26
                patient['治疗前HAMD17'] = pre_score
            else:  # 观察组
                # 治疗前：17.98±3.35
                pre_score = np.random.normal(17.98, 3.35)
                pre_score = round(max(7, min(26, pre_score)), 1)
                patient['治疗前HAMD17'] = pre_score
        
        return patients
    
    def generate_kps_scores(self, patients):
        """生成KPS功能评分数据"""
        for patient in patients:
            if patient['分组'] == '对照组':
                # 治疗前：66.19±2.93
                pre_score = np.random.normal(66.19, 2.93)
                pre_score = int(round(max(40, min(80, pre_score)) / 10) * 10)  # KPS通常以10为单位
                patient['治疗前KPS'] = pre_score
            else:  # 观察组
                # 治疗前：65.86±3.06
                pre_score = np.random.normal(65.86, 3.06)
                pre_score = int(round(max(40, min(80, pre_score)) / 10) * 10)
                patient['治疗前KPS'] = pre_score
        
        return patients
    
    def generate_tcm_efficacy(self, patients):
        """生成中医证候疗效数据"""
        control_patients = [p for p in patients if p['分组'] == '对照组']
        observation_patients = [p for p in patients if p['分组'] == '观察组']
        
        # 对照组疗效分配
        # 临床痊愈：1例(1.89%)，显效：17例(32.08%)，有效：20例(37.74%)，无效：15例(28.30%)
        control_efficacy = ['临床痊愈'] * 1 + ['显效'] * 17 + ['有效'] * 20 + ['无效'] * 15
        random.shuffle(control_efficacy)
        
        for i, patient in enumerate(control_patients):
            patient['中医证候疗效'] = control_efficacy[i]
        
        # 观察组疗效分配
        # 临床痊愈：5例(9.43%)，显效：21例(39.62%)，有效：22例(41.51%)，无效：5例(9.43%)
        observation_efficacy = ['临床痊愈'] * 5 + ['显效'] * 21 + ['有效'] * 22 + ['无效'] * 5
        random.shuffle(observation_efficacy)
        
        for i, patient in enumerate(observation_patients):
            patient['中医证候疗效'] = observation_efficacy[i]
        
        return patients
    
    def generate_post_treatment_scores(self, patients):
        """生成治疗后评分数据"""
        for patient in patients:
            efficacy = patient['中医证候疗效']
            
            if patient['分组'] == '对照组':
                # 治疗后HAMD-17：14.31±3.51
                base_hamd = 14.31
                hamd_std = 3.51
                
                # 治疗后KPS：71.52±4.72
                base_kps = 71.52
                kps_std = 4.72
                
                # 根据疗效调整评分
                if efficacy == '临床痊愈':
                    hamd_mult = 0.6  # 更好的改善
                    kps_mult = 1.3
                elif efficacy == '显效':
                    hamd_mult = 0.8
                    kps_mult = 1.2
                elif efficacy == '有效':
                    hamd_mult = 0.9
                    kps_mult = 1.1
                else:  # 无效
                    hamd_mult = 1.1  # 改善较少
                    kps_mult = 0.95
                
            else:  # 观察组
                # 治疗后HAMD-17：11.12±3.82
                base_hamd = 11.12
                hamd_std = 3.82
                
                # 治疗后KPS：78.38±4.53
                base_kps = 78.38
                kps_std = 4.53
                
                # 根据疗效调整评分
                if efficacy == '临床痊愈':
                    hamd_mult = 0.5
                    kps_mult = 1.25
                elif efficacy == '显效':
                    hamd_mult = 0.7
                    kps_mult = 1.15
                elif efficacy == '有效':
                    hamd_mult = 0.85
                    kps_mult = 1.05
                else:  # 无效
                    hamd_mult = 1.2
                    kps_mult = 0.9
            
            # 生成治疗后HAMD-17评分
            post_hamd = np.random.normal(base_hamd * hamd_mult, hamd_std)
            post_hamd = round(max(0, min(patient['治疗前HAMD17'] - 1, post_hamd)), 1)
            patient['治疗后HAMD17'] = post_hamd
            
            # 生成治疗后KPS评分
            post_kps = np.random.normal(base_kps * kps_mult, kps_std)
            post_kps = int(round(max(patient['治疗前KPS'], min(100, post_kps)) / 10) * 10)
            patient['治疗后KPS'] = post_kps
        
        return patients
    
    def validate_statistics(self, patients):
        """验证统计结果"""
        control_patients = [p for p in patients if p['分组'] == '对照组']
        observation_patients = [p for p in patients if p['分组'] == '观察组']
        
        print("=== 统计验证结果 ===")
        
        # 验证基线特征
        print("\n1. 基线特征验证:")
        
        # 对照组
        control_male = len([p for p in control_patients if p['性别'] == '男'])
        control_female = len([p for p in control_patients if p['性别'] == '女'])
        control_ages = [p['年龄'] for p in control_patients]
        control_age_mean = np.mean(control_ages)
        control_age_std = np.std(control_ages, ddof=1)
        
        print(f"对照组: 男{control_male}例/女{control_female}例, 年龄{control_age_mean:.2f}±{control_age_std:.2f}岁")
        
        # 观察组
        obs_male = len([p for p in observation_patients if p['性别'] == '男'])
        obs_female = len([p for p in observation_patients if p['性别'] == '女'])
        obs_ages = [p['年龄'] for p in observation_patients]
        obs_age_mean = np.mean(obs_ages)
        obs_age_std = np.std(obs_ages, ddof=1)
        
        print(f"观察组: 男{obs_male}例/女{obs_female}例, 年龄{obs_age_mean:.2f}±{obs_age_std:.2f}岁")
        
        # 验证疗效分布
        print("\n2. 疗效分布验证:")
        
        # 对照组疗效
        control_efficacy = {}
        for efficacy in ['临床痊愈', '显效', '有效', '无效']:
            count = len([p for p in control_patients if p['中医证候疗效'] == efficacy])
            percentage = count / len(control_patients) * 100
            control_efficacy[efficacy] = count
            print(f"对照组{efficacy}: {count}例 ({percentage:.2f}%)")
        
        control_effective = control_efficacy['临床痊愈'] + control_efficacy['显效'] + control_efficacy['有效']
        control_total_rate = control_effective / len(control_patients) * 100
        print(f"对照组总有效率: {control_total_rate:.2f}%")
        
        # 观察组疗效
        obs_efficacy = {}
        for efficacy in ['临床痊愈', '显效', '有效', '无效']:
            count = len([p for p in observation_patients if p['中医证候疗效'] == efficacy])
            percentage = count / len(observation_patients) * 100
            obs_efficacy[efficacy] = count
            print(f"观察组{efficacy}: {count}例 ({percentage:.2f}%)")
        
        obs_effective = obs_efficacy['临床痊愈'] + obs_efficacy['显效'] + obs_efficacy['有效']
        obs_total_rate = obs_effective / len(observation_patients) * 100
        print(f"观察组总有效率: {obs_total_rate:.2f}%")
        
        # 验证HAMD-17评分
        print("\n3. HAMD-17评分验证:")
        
        control_pre_hamd = [p['治疗前HAMD17'] for p in control_patients]
        control_post_hamd = [p['治疗后HAMD17'] for p in control_patients]
        obs_pre_hamd = [p['治疗前HAMD17'] for p in observation_patients]
        obs_post_hamd = [p['治疗后HAMD17'] for p in observation_patients]
        
        print(f"对照组治疗前: {np.mean(control_pre_hamd):.2f}±{np.std(control_pre_hamd, ddof=1):.2f}")
        print(f"对照组治疗后: {np.mean(control_post_hamd):.2f}±{np.std(control_post_hamd, ddof=1):.2f}")
        print(f"观察组治疗前: {np.mean(obs_pre_hamd):.2f}±{np.std(obs_pre_hamd, ddof=1):.2f}")
        print(f"观察组治疗后: {np.mean(obs_post_hamd):.2f}±{np.std(obs_post_hamd, ddof=1):.2f}")
        
        # 验证KPS评分
        print("\n4. KPS评分验证:")
        
        control_pre_kps = [p['治疗前KPS'] for p in control_patients]
        control_post_kps = [p['治疗后KPS'] for p in control_patients]
        obs_pre_kps = [p['治疗前KPS'] for p in observation_patients]
        obs_post_kps = [p['治疗后KPS'] for p in observation_patients]
        
        print(f"对照组治疗前: {np.mean(control_pre_kps):.2f}±{np.std(control_pre_kps, ddof=1):.2f}")
        print(f"对照组治疗后: {np.mean(control_post_kps):.2f}±{np.std(control_post_kps, ddof=1):.2f}")
        print(f"观察组治疗前: {np.mean(obs_pre_kps):.2f}±{np.std(obs_pre_kps, ddof=1):.2f}")
        print(f"观察组治疗后: {np.mean(obs_post_kps):.2f}±{np.std(obs_post_kps, ddof=1):.2f}")
        
        return patients
    
    def generate_complete_dataset(self):
        """生成完整数据集"""
        print("开始生成医学研究数据集...")
        
        # 1. 生成基线特征
        patients = self.generate_baseline_characteristics()
        print("✓ 基线特征数据生成完成")
        
        # 2. 生成治疗前评分
        patients = self.generate_hamd_scores(patients)
        patients = self.generate_kps_scores(patients)
        print("✓ 治疗前评分数据生成完成")
        
        # 3. 生成疗效分类
        patients = self.generate_tcm_efficacy(patients)
        print("✓ 中医证候疗效数据生成完成")
        
        # 4. 生成治疗后评分
        patients = self.generate_post_treatment_scores(patients)
        print("✓ 治疗后评分数据生成完成")
        
        # 5. 验证统计结果
        patients = self.validate_statistics(patients)
        
        return patients

def main():
    generator = MedicalDatasetGenerator()
    patients_data = generator.generate_complete_dataset()
    
    # 转换为DataFrame
    df = pd.DataFrame(patients_data)
    
    # 重新排列列的顺序
    columns_order = [
        '患者ID', '姓名', '性别', '年龄', 'TNM分期', '分组',
        '治疗前HAMD17', '治疗后HAMD17', '治疗前KPS', '治疗后KPS',
        '中医证候疗效'
    ]
    df = df[columns_order]
    
    # 保存数据
    output_file = '/Users/<USER>/Downloads/冯雷/原始数据/医学研究数据集_106例.xlsx'
    df.to_excel(output_file, index=False, sheet_name='研究数据')
    
    # 同时保存CSV格式
    csv_file = '/Users/<USER>/Downloads/冯雷/原始数据/医学研究数据集_106例.csv'
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    print(f"\n=== 数据集生成完成 ===")
    print(f"Excel文件: {output_file}")
    print(f"CSV文件: {csv_file}")
    print(f"总样本量: {len(df)}例")
    print(f"对照组: {len(df[df['分组'] == '对照组'])}例")
    print(f"观察组: {len(df[df['分组'] == '观察组'])}例")
    
    return df

if __name__ == "__main__":
    dataset = main()