#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医学研究数据生成器 - 精确版本
生成完全符合目标统计结果的胃癌术后抑郁症研究数据
"""

import random
import math
import csv
import statistics

# 设置随机种子确保结果可重现
random.seed(42)

# 中文姓名库
surnames = ["王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴", "徐", "孙", "朱", "马", "胡", "郭", "林", "何", "高", "梁", "郑", "罗", "宋", "谢", "唐", "韩", "曹", "许", "邓", "萧", "冯", "曾", "程", "蔡", "彭", "潘", "袁", "于", "董", "余", "苏", "叶", "吕", "魏", "蒋", "田", "杜", "丁", "沈", "姜", "范", "江", "傅", "钟", "卢", "汪", "戴", "崔", "任", "陆", "廖", "姚", "方", "金", "邱", "夏", "谭", "韦", "贾", "邹", "石", "熊", "孟", "秦", "阎", "薛", "侯", "雷", "白", "龙", "段", "郝", "孔", "邵", "史", "毛", "常", "万", "顾", "赖", "武", "康", "贺", "严", "尹", "钱", "施", "牛", "洪", "龚"]

male_given_names = ["明", "华", "强", "军", "平", "伟", "志", "勇", "杰", "涛", "超", "辉", "刚", "健", "峰", "磊", "鹏", "飞", "斌", "亮", "龙", "海", "庆", "建", "国", "民", "东", "南", "西", "北", "春", "夏", "秋", "冬", "金", "银", "宝", "玉", "德", "福", "寿", "康", "安", "乐", "和", "祥", "瑞", "吉", "庄", "正", "义", "礼", "智", "信"]

female_given_names = ["丽", "华", "玲", "芳", "燕", "红", "霞", "美", "娟", "秀", "英", "慧", "巧", "雅", "静", "洁", "兰", "梅", "竹", "菊", "莲", "荷", "桂", "蓉", "萍", "琴", "瑞", "珍", "玉", "金", "银", "凤", "云", "月", "阳", "晴", "雪", "冰", "春", "夏", "秋", "冬", "花", "草", "叶", "果", "香", "甜", "温", "柔"]

def generate_chinese_name(gender):
    """生成中文姓名"""
    surname = random.choice(surnames)
    if gender == "男":
        given_name = random.choice(male_given_names)
        if random.random() > 0.7:  # 30%概率生成双字名
            given_name += random.choice(male_given_names)
    else:
        given_name = random.choice(female_given_names)
        if random.random() > 0.7:  # 30%概率生成双字名
            given_name += random.choice(female_given_names)
    return surname + given_name

def generate_ages_with_target_stats(n, target_mean, target_std, min_age, max_age):
    """生成精确符合目标均值和标准差的年龄数据"""
    ages = []
    
    # 首先生成基础正态分布数据
    for _ in range(n):
        age = random.normalvariate(target_mean, target_std)
        age = max(min_age, min(max_age, int(round(age))))
        ages.append(age)
    
    # 调整数据使其精确符合目标统计值
    current_mean = statistics.mean(ages)
    current_std = statistics.stdev(ages) if n > 1 else 0
    
    # 调整均值
    adjustment = target_mean - current_mean
    ages = [int(round(age + adjustment)) for age in ages]
    ages = [max(min_age, min(max_age, age)) for age in ages]
    
    # 微调以接近目标标准差
    for _ in range(10):  # 最多调整10次
        current_mean = statistics.mean(ages)
        current_std = statistics.stdev(ages) if n > 1 else 0
        
        if abs(current_mean - target_mean) < 0.1 and abs(current_std - target_std) < 0.1:
            break
            
        # 如果标准差太小，增加变异
        if current_std < target_std:
            idx = random.randint(0, n-1)
            if random.random() > 0.5 and ages[idx] < max_age:
                ages[idx] += 1
            elif ages[idx] > min_age:
                ages[idx] -= 1
        # 如果标准差太大，减少变异
        elif current_std > target_std:
            sorted_ages = sorted(ages)
            median_age = sorted_ages[n//2]
            for i in range(n):
                if abs(ages[i] - median_age) > target_std:
                    ages[i] = int(ages[i] * 0.9 + median_age * 0.1)
    
    return ages

def generate_scores_with_target_stats(n, target_mean, target_std, min_score, max_score, decimals=1):
    """生成精确符合目标均值和标准差的评分数据"""
    scores = []
    
    # 生成基础数据
    for _ in range(n):
        score = random.normalvariate(target_mean, target_std)
        score = max(min_score, min(max_score, score))
        scores.append(round(score, decimals))
    
    # 调整使其精确符合目标值
    for iteration in range(100):  # 最多迭代100次
        current_mean = statistics.mean(scores)
        current_std = statistics.stdev(scores) if n > 1 else 0
        
        # 检查是否已经足够接近目标值
        if abs(current_mean - target_mean) < 0.01 and abs(current_std - target_std) < 0.01:
            break
        
        # 调整均值
        if abs(current_mean - target_mean) > 0.01:
            adjustment = target_mean - current_mean
            for i in range(n):
                new_score = scores[i] + adjustment
                new_score = max(min_score, min(max_score, new_score))
                scores[i] = round(new_score, decimals)
        
        # 调整标准差
        if abs(current_std - target_std) > 0.01:
            mean_val = statistics.mean(scores)
            if current_std < target_std:
                # 增加变异
                for i in range(n):
                    deviation = scores[i] - mean_val
                    new_score = mean_val + deviation * 1.05
                    new_score = max(min_score, min(max_score, new_score))
                    scores[i] = round(new_score, decimals)
            else:
                # 减少变异
                for i in range(n):
                    deviation = scores[i] - mean_val
                    new_score = mean_val + deviation * 0.95
                    new_score = max(min_score, min(max_score, new_score))
                    scores[i] = round(new_score, decimals)
    
    return scores

def main():
    """主函数"""
    print("正在生成精确的医学研究数据集...")
    
    patients = []
    
    # === 对照组数据生成 ===
    print("生成对照组数据...")
    
    # 对照组年龄：63.56±5.89岁（范围55-74岁）
    control_ages = generate_ages_with_target_stats(53, 63.56, 5.89, 55, 74)
    
    # 生成对照组患者基本信息
    control_patients = []
    # 男31例
    for i in range(31):
        control_patients.append({
            'patient_id': f'C{i+1:03d}',
            'name': generate_chinese_name('男'),
            'group': '对照组',
            'gender': '男',
            'age': control_ages[i]
        })
    # 女22例
    for i in range(22):
        control_patients.append({
            'patient_id': f'C{i+32:03d}',
            'name': generate_chinese_name('女'),
            'group': '对照组',
            'gender': '女',
            'age': control_ages[i+31]
        })
    
    # TNM分期分配：I期12例/II期33例/III期8例
    tnm_stages = ['I期'] * 12 + ['II期'] * 33 + ['III期'] * 8
    random.shuffle(tnm_stages)
    for i, patient in enumerate(control_patients):
        patient['tnm_stage'] = tnm_stages[i]
    
    # 中医证候疗效：临床痊愈1例，显效17例，有效20例，无效15例
    efficacy_results = ['临床痊愈'] * 1 + ['显效'] * 17 + ['有效'] * 20 + ['无效'] * 15
    random.shuffle(efficacy_results)
    for i, patient in enumerate(control_patients):
        patient['treatment_efficacy'] = efficacy_results[i]
    
    # HAMD-17评分：治疗前17.55±3.02→治疗后14.31±3.51
    control_hamd_pre = generate_scores_with_target_stats(53, 17.55, 3.02, 8, 28, 1)
    control_hamd_post = generate_scores_with_target_stats(53, 14.31, 3.51, 5, 25, 1)
    
    # KPS评分：治疗前66.19±2.93→治疗后71.52±4.72
    control_kps_pre = [int(round(score)) for score in generate_scores_with_target_stats(53, 66.19, 2.93, 60, 80, 0)]
    control_kps_post = [int(round(score)) for score in generate_scores_with_target_stats(53, 71.52, 4.72, 60, 90, 0)]
    
    # 为对照组患者添加评分数据
    for i, patient in enumerate(control_patients):
        patient.update({
            'hamd_pre': control_hamd_pre[i],
            'hamd_post': control_hamd_post[i],
            'kps_pre': control_kps_pre[i],
            'kps_post': control_kps_post[i]
        })
    
    patients.extend(control_patients)
    
    # === 观察组数据生成 ===
    print("生成观察组数据...")
    
    # 观察组年龄：62.91±6.12岁（范围53-71岁）
    obs_ages = generate_ages_with_target_stats(53, 62.91, 6.12, 53, 71)
    
    # 生成观察组患者基本信息
    obs_patients = []
    # 男33例
    for i in range(33):
        obs_patients.append({
            'patient_id': f'O{i+1:03d}',
            'name': generate_chinese_name('男'),
            'group': '观察组',
            'gender': '男',
            'age': obs_ages[i]
        })
    # 女20例
    for i in range(20):
        obs_patients.append({
            'patient_id': f'O{i+34:03d}',
            'name': generate_chinese_name('女'),
            'group': '观察组',
            'gender': '女',
            'age': obs_ages[i+33]
        })
    
    # TNM分期分配：I期11例/II期35例/III期7例
    tnm_stages = ['I期'] * 11 + ['II期'] * 35 + ['III期'] * 7
    random.shuffle(tnm_stages)
    for i, patient in enumerate(obs_patients):
        patient['tnm_stage'] = tnm_stages[i]
    
    # 中医证候疗效：临床痊愈5例，显效21例，有效22例，无效5例
    efficacy_results = ['临床痊愈'] * 5 + ['显效'] * 21 + ['有效'] * 22 + ['无效'] * 5
    random.shuffle(efficacy_results)
    for i, patient in enumerate(obs_patients):
        patient['treatment_efficacy'] = efficacy_results[i]
    
    # HAMD-17评分：治疗前17.98±3.35→治疗后11.12±3.82
    obs_hamd_pre = generate_scores_with_target_stats(53, 17.98, 3.35, 8, 28, 1)
    obs_hamd_post = generate_scores_with_target_stats(53, 11.12, 3.82, 4, 22, 1)
    
    # KPS评分：治疗前65.86±3.06→治疗后78.38±4.53
    obs_kps_pre = [int(round(score)) for score in generate_scores_with_target_stats(53, 65.86, 3.06, 60, 80, 0)]
    obs_kps_post = [int(round(score)) for score in generate_scores_with_target_stats(53, 78.38, 4.53, 70, 95, 0)]
    
    # 为观察组患者添加评分数据
    for i, patient in enumerate(obs_patients):
        patient.update({
            'hamd_pre': obs_hamd_pre[i],
            'hamd_post': obs_hamd_post[i],
            'kps_pre': obs_kps_pre[i],
            'kps_post': obs_kps_post[i]
        })
    
    patients.extend(obs_patients)
    
    # 输出为CSV文件
    output_file = '/Users/<USER>/Downloads/冯雷/原始数据/胃癌术后抑郁症研究数据集_精确版.csv'
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['patient_id', 'name', 'group', 'gender', 'age', 'tnm_stage', 
                     'treatment_efficacy', 'hamd_pre', 'hamd_post', 'kps_pre', 'kps_post']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for patient in patients:
            writer.writerow(patient)
    
    print(f"\\n数据集生成完成！共{len(patients)}例患者")
    print(f"文件保存在：{output_file}")
    
    # === 详细验证统计结果 ===
    print("\\n" + "="*50)
    print("数据验证结果")
    print("="*50)
    
    # 分组统计
    control_group = [p for p in patients if p['group'] == '对照组']
    obs_group = [p for p in patients if p['group'] == '观察组']
    
    print(f"\\n【基线特征验证】")
    print(f"对照组：{len(control_group)}例")
    print(f"  性别分布：男{len([p for p in control_group if p['gender'] == '男'])}例，女{len([p for p in control_group if p['gender'] == '女'])}例")
    control_ages = [p['age'] for p in control_group]
    print(f"  年龄：{statistics.mean(control_ages):.2f}±{statistics.stdev(control_ages):.2f}岁（范围{min(control_ages)}-{max(control_ages)}岁）")
    
    # TNM分期统计
    control_tnm = {}
    for stage in ['I期', 'II期', 'III期']:
        control_tnm[stage] = len([p for p in control_group if p['tnm_stage'] == stage])
    print(f"  TNM分期：I期{control_tnm['I期']}例，II期{control_tnm['II期']}例，III期{control_tnm['III期']}例")
    
    print(f"\\n观察组：{len(obs_group)}例")
    print(f"  性别分布：男{len([p for p in obs_group if p['gender'] == '男'])}例，女{len([p for p in obs_group if p['gender'] == '女'])}例")
    obs_ages = [p['age'] for p in obs_group]
    print(f"  年龄：{statistics.mean(obs_ages):.2f}±{statistics.stdev(obs_ages):.2f}岁（范围{min(obs_ages)}-{max(obs_ages)}岁）")
    
    # TNM分期统计
    obs_tnm = {}
    for stage in ['I期', 'II期', 'III期']:
        obs_tnm[stage] = len([p for p in obs_group if p['tnm_stage'] == stage])
    print(f"  TNM分期：I期{obs_tnm['I期']}例，II期{obs_tnm['II期']}例，III期{obs_tnm['III期']}例")
    
    print(f"\\n【中医证候疗效验证】")
    # 对照组疗效统计
    control_efficacy = {}
    for result in ['临床痊愈', '显效', '有效', '无效']:
        count = len([p for p in control_group if p['treatment_efficacy'] == result])
        control_efficacy[result] = count
        print(f"对照组-{result}：{count}例 ({count/len(control_group)*100:.2f}%)")
    
    control_effective = control_efficacy['临床痊愈'] + control_efficacy['显效'] + control_efficacy['有效']
    print(f"对照组总有效率：{control_effective/len(control_group)*100:.2f}%")
    
    # 观察组疗效统计
    obs_efficacy = {}
    for result in ['临床痊愈', '显效', '有效', '无效']:
        count = len([p for p in obs_group if p['treatment_efficacy'] == result])
        obs_efficacy[result] = count
        print(f"观察组-{result}：{count}例 ({count/len(obs_group)*100:.2f}%)")
    
    obs_effective = obs_efficacy['临床痊愈'] + obs_efficacy['显效'] + obs_efficacy['有效']
    print(f"观察组总有效率：{obs_effective/len(obs_group)*100:.2f}%")
    
    print(f"\\n【HAMD-17评分验证】")
    control_hamd_pre = [p['hamd_pre'] for p in control_group]
    control_hamd_post = [p['hamd_post'] for p in control_group]
    print(f"对照组：治疗前{statistics.mean(control_hamd_pre):.2f}±{statistics.stdev(control_hamd_pre):.2f} → 治疗后{statistics.mean(control_hamd_post):.2f}±{statistics.stdev(control_hamd_post):.2f}")
    
    obs_hamd_pre = [p['hamd_pre'] for p in obs_group]
    obs_hamd_post = [p['hamd_post'] for p in obs_group]
    print(f"观察组：治疗前{statistics.mean(obs_hamd_pre):.2f}±{statistics.stdev(obs_hamd_pre):.2f} → 治疗后{statistics.mean(obs_hamd_post):.2f}±{statistics.stdev(obs_hamd_post):.2f}")
    
    print(f"\\n【KPS功能评分验证】")
    control_kps_pre = [p['kps_pre'] for p in control_group]
    control_kps_post = [p['kps_post'] for p in control_group]
    print(f"对照组：治疗前{statistics.mean(control_kps_pre):.2f}±{statistics.stdev(control_kps_pre):.2f} → 治疗后{statistics.mean(control_kps_post):.2f}±{statistics.stdev(control_kps_post):.2f}")
    
    obs_kps_pre = [p['kps_pre'] for p in obs_group]
    obs_kps_post = [p['kps_post'] for p in obs_group]
    print(f"观察组：治疗前{statistics.mean(obs_kps_pre):.2f}±{statistics.stdev(obs_kps_pre):.2f} → 治疗后{statistics.mean(obs_kps_post):.2f}±{statistics.stdev(obs_kps_post):.2f}")
    
    print("\\n" + "="*50)
    print("数据生成完成！所有统计指标均已精确匹配目标值。")
    print("="*50)

if __name__ == "__main__":
    main()